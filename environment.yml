name: RL_task
channels:
  - pytorch
  - conda-forge
  - defaults
dependencies:
  - asttokens=3.0.0=pyhd8ed1ab_1
  - blas=1.0=mkl
  - brotli-python=1.0.9=py39h5da7b33_9
  - ca-certificates=2025.2.25=haa95532_0
  - certifi=2025.1.31=pyhd8ed1ab_0
  - charset-normalizer=3.3.2=pyhd3eb1b0_0
  - colorama=0.4.6=pyhd8ed1ab_1
  - comm=0.2.2=pyhd8ed1ab_1
  - cpython=3.9.21=py39hd8ed1ab_1
  - cudatoolkit=11.3.1=h59b6b97_2
  - debugpy=1.8.13=py39ha51f57c_0
  - decorator=5.2.1=pyhd8ed1ab_0
  - exceptiongroup=1.2.2=pyhd8ed1ab_1
  - executing=2.1.0=pyhd8ed1ab_1
  - freetype=2.12.1=ha860e81_0
  - idna=3.7=py39haa95532_0
  - intel-openmp=2023.1.0=h59b6b97_46320
  - ipykernel=6.29.5=pyh4bbf305_0
  - ipython=8.18.1=pyh7428d3b_3
  - jedi=0.19.2=pyhd8ed1ab_1
  - jpeg=9e=h827c3e9_3
  - jupyter_client=8.6.3=pyhd8ed1ab_1
  - jupyter_core=5.7.2=pyh5737063_1
  - krb5=1.21.3=hdf4eb48_0
  - lcms2=2.16=hb4a4139_0
  - lerc=4.0.0=h5da7b33_0
  - libdeflate=1.22=h5bf469e_0
  - libpng=1.6.39=h8cc25b3_0
  - libsodium=1.0.20=hc70643c_0
  - libtiff=4.5.1=h44ae7cf_1
  - libuv=1.48.0=h827c3e9_0
  - libwebp-base=1.3.2=h3d04722_1
  - lz4-c=1.9.4=h2bbff1b_1
  - matplotlib-inline=0.1.7=pyhd8ed1ab_1
  - mkl=2023.1.0=h6b88ed4_46358
  - mkl-service=2.4.0=py39h827c3e9_2
  - mkl_fft=1.3.11=py39h827c3e9_0
  - mkl_random=1.2.8=py39hc64d2fc_0
  - nest-asyncio=1.6.0=pyhd8ed1ab_1
  - openjpeg=2.5.2=hae555c5_0
  - openssl=3.4.1=ha4e3fda_0
  - packaging=24.2=pyhd8ed1ab_2
  - parso=0.8.4=pyhd8ed1ab_1
  - pickleshare=0.7.5=pyhd8ed1ab_1004
  - pillow=11.1.0=py39h096bfcc_0
  - platformdirs=4.3.6=pyhd8ed1ab_1
  - prompt-toolkit=3.0.50=pyha770c72_0
  - psutil=7.0.0=py39ha55e580_0
  - pure_eval=0.2.3=pyhd8ed1ab_1
  - pygments=2.19.1=pyhd8ed1ab_0
  - pysocks=1.7.1=py39haa95532_0
  - python=3.9.21=h8205438_1
  - python-dateutil=2.9.0.post0=pyhff2d567_1
  - python_abi=3.9=2_cp39
  - pytorch=1.11.0=py3.9_cuda11.3_cudnn8_0
  - pytorch-mutex=1.0=cuda
  - pywin32=307=py39ha51f57c_3
  - pyzmq=26.3.0=py39h03e5c00_0
  - requests=2.32.3=py39haa95532_1
  - six=1.17.0=pyhd8ed1ab_0
  - sqlite=3.45.3=h2bbff1b_0
  - stack_data=0.6.3=pyhd8ed1ab_1
  - tbb=2021.8.0=h59b6b97_0
  - torchaudio=0.11.0=py39_cu113
  - torchvision=0.12.0=py39_cu113
  - tornado=6.4.2=py39ha55e580_0
  - traitlets=5.14.3=pyhd8ed1ab_1
  - typing_extensions=4.12.2=py39haa95532_0
  - ucrt=10.0.22621.0=h57928b3_1
  - urllib3=2.3.0=py39haa95532_0
  - vc=14.42=haa95532_4
  - vc14_runtime=14.42.34438=hfd919c2_24
  - vs2015_runtime=14.42.34438=h7142326_24
  - wcwidth=0.2.13=pyhd8ed1ab_1
  - wheel=0.45.1=py39haa95532_0
  - win_inet_pton=1.1.0=py39haa95532_0
  - xz=5.6.4=h4754444_1
  - zeromq=4.3.5=ha9f60a1_7
  - zipp=3.21.0=pyhd8ed1ab_1
  - zlib=1.2.13=h8cc25b3_1
  - zstd=1.5.6=h8880b57_0
  - pip:
      - absl-py==2.1.0
      - ale-py==0.7.4
      - astor==0.8.1
      - autorom==0.4.2
      - autorom-accept-rom-license==0.6.1
      - box2d-py==2.3.5
      - cloudpickle==3.1.1
      - contourpy==1.3.0
      - cycler==0.12.1
      - filelock==3.11.0
      - fonttools==4.56.0
      - grpcio==1.71.0
      - gym==0.26.2
      - gym-notices==0.0.8
      - importlib-metadata==4.13.0
      - importlib-resources==6.5.2
      - joblib==1.4.2
      - json-tricks==3.17.3
      - kiwisolver==1.4.7
      - libtorrent==2.0.11
      - loguru==0.7.3
      - markdown==3.7
      - markdown-it-py==3.0.0
      - markupsafe==3.0.2
      - mdurl==0.1.2
      - nni==3.0
      - numpy==1.26.4
      - nvidia-ml-py==12.570.86
      - opencv-python==*********
      - pandas==2.2.3
      - pip==24.0
      - prettytable==3.15.1
      - protobuf==6.30.1
      - pygame==2.1.0
      - pyglet==1.5.11
      - pyparsing==3.2.1
      - pythonwebhdfs==0.2.3
      - pytz==2025.1
      - pyyaml==6.0.2
      - responses==0.25.7
      - rich==13.9.4
      - schema==0.7.7
      - scikit-learn==1.6.1
      - scipy==1.13.1
      - setuptools==65.5.0
      - simplejson==3.20.1
      - stable-baselines3==1.8.0a10
      - swig==4.3.0
      - tensorboard==2.19.0
      - tensorboard-data-server==0.7.2
      - threadpoolctl==3.6.0
      - tqdm==4.67.1
      - typeguard==4.1.2
      - tzdata==2025.1
      - websockets==15.0.1
      - werkzeug==3.1.3
      - win32-setctime==1.2.0
prefix: D:\Anaconda\envs\RL_task
