from dataclasses import dataclass

import gym
import numpy as np
from gym import spaces


@dataclass
class UAVConfig:
    N: int = 8  # 终端数量
    M: int = 2  # 无人机数量
    L: float = 100.0  # 区域长度
    W: float = 100.0  # 区域宽度
    H: float = 100.0  # 高度
    M_uav: float = 9.65  # UAV载重
    a_0: int = -50  # 信道增益
    sigma_2: int = -130  # 接收器的噪声功率
    P_up: float = 0.1  # W，传输功率
    P_nloss: int = -110  # dB, 遮挡后的信噪比
    B: float = 1e6  # Hz，传输带宽
    T: int = 3200  # 一个周期时间
    t_fly: int = 1  # 飞行时间
    t_com: int = 7  # 计算时间
    delta_t: int = t_fly + t_com  # 一个周期时间
    slot_num: int = T // delta_t  # 一个周期内的步数
    v_ue: int = 1  # 终端移动速度
    v_max: int = 50  # 最大飞行速率
    cpu_cycles: int = 1e3  # CPU周期数
    f_ue: float = 0.2e9  # UE计算频率
    E_b: int = 5000e3  # 电池容量:J
    f_uav: float = 1.2e9  # UAV计算频率
    delta: float = 10e-27  # 终端能量转换效率
    k: float = 10e-27  # UAV能量转换效率
    D_total: int = 600e6 * M  # 每个周期内的总数据量
    kappa: float = 0.01  # 动态权重系数
    V: float = 100  # Lyapunov drift plus penalty的权重
    w_T: float = 1.0  # 时延权重
    w_E: float = 0.5  # 能耗权重


class UAVEnv(gym.Env):
    def __init__(self, max_steps=1000, v=1, M=1, N=4, D_total=1200e6):
        super(UAVEnv, self).__init__()
        self.uav_config = UAVConfig(V=v, M=M, N=N, D_total=D_total * M)
        self.max_steps = self.uav_config.T
        self.current_step = 0
        self.failed_count = 0
        self.sum_cost = 0
        self.average_T = 0
        self.average_E = 0
        self.name = "李雅普诺夫优化"  # 添加环境名称属性

        # 任务队列 - 仅用于计算奖励
        self.Q_t = 0

        """非观测状态"""
        self.a_n = np.zeros(
            (self.uav_config.M, self.uav_config.N)
        )  # 每个UAV当前时刻选择的终端，onehot编码
        self.D_r = np.array([self.uav_config.D_total], dtype=np.float32)

        # 状态空间定义 - 移除队列状态
        # UAV剩余电量(M) + UAV位置(M*2) + 剩余数据量(1) + 终端位置(N*2) + 终端数据量(N) + 终端遮挡状态(N)
        self.state_dim = self.uav_config.M * 3 + 1 + self.uav_config.N * 4
        self.observation_space = spaces.Box(
            low=np.zeros(self.state_dim),
            high=np.concatenate(
                [
                    np.tile(
                        np.array([self.uav_config.E_b]), self.uav_config.M
                    ),  # 每个UAV的电池容量
                    np.tile(
                        np.array([self.uav_config.L, self.uav_config.W]),
                        self.uav_config.M,
                    ),  # 每个UAV的位置范围
                    np.array([self.uav_config.D_total]),  # 总剩余数据量
                    np.tile(
                        np.array([self.uav_config.L, self.uav_config.W]),
                        self.uav_config.N,
                    ),  # 终端位置范围
                    np.tile(
                        np.array([self.uav_config.D_total]), self.uav_config.N
                    ),  # 终端数据量
                    np.ones(self.uav_config.N),  # 终端遮挡状态
                ]
            ),
            dtype=np.float32,
        )

        # 动作空间: 每个UAV都有[选择终端, 飞行角度, 飞行速度, 卸载率]四个动作
        self.action_space = spaces.Box(
            low=np.tile(np.array([0, 0, 0, 0], dtype=np.float32), self.uav_config.M),
            high=np.tile(
                np.array(
                    [self.uav_config.N - 1, 2 * np.pi, self.uav_config.v_max, 1],
                    dtype=np.float32,
                ),
                self.uav_config.M,
            ),
            dtype=np.float32,
        )

    def reset(self):
        # 初始化随机状态
        self.current_step = 0
        self.sum_cost = 0
        self.average_T = 0
        self.average_E = 0

        # 初始化队列
        self.Q_t = 0

        # 初始化每个UAV的电池容量
        E_b = np.full(self.uav_config.M, self.uav_config.E_b, dtype=np.float32)
        D_r = self.uav_config.D_total

        # 初始化每个UAV的位置 - 随机分布在区域内
        loc_uav = np.random.uniform(
            low=[0, 0],
            high=[self.uav_config.L, self.uav_config.W],
            size=(self.uav_config.M, 2),
        )
        loc_uav = np.tile([50, 50], (self.uav_config.M, 1))

        # 终端位置
        loc_ue = np.random.uniform(
            0, [self.uav_config.L, self.uav_config.W], size=(self.uav_config.N, 2)
        )
        # 终端初始任务量
        task_size = np.minimum(
            np.random.normal(
                self.uav_config.D_total / (self.uav_config.slot_num),
                scale=self.uav_config.D_total / (4 * self.uav_config.slot_num),
                size=self.uav_config.N,
            ),
            self.uav_config.D_total,
        ).astype(np.float32)
        # 终端遮挡状态
        block_flag = np.random.choice([0, 1], size=self.uav_config.N, p=[0.5, 0.5])

        # 构建状态列表
        state_list = []
        state_list.extend(E_b.tolist())  # 每个UAV的电池容量
        state_list.extend(loc_uav.flatten().tolist())  # 每个UAV的位置
        state_list.append(D_r)  # 总剩余数据量
        state_list.extend(loc_ue.flatten().tolist())  # 终端位置
        state_list.extend(task_size.tolist())
        state_list.extend(block_flag.tolist())

        self.state = np.array(state_list, dtype=np.float32)
        return self.state.copy()

    def _calc_relay_ue_computation(self, R_n, a_n, D_n):
        """计算终端本地计算时延

        Args:
            R_n: 终端任务卸载率
            a_n: 当前选择的终端(one-hot编码)
            D_n: 终端任务数据量

        Returns:
            float: 终端本地计算时延,对应公式3-5
        """
        f_ue = self.uav_config.f_ue
        C = self.uav_config.cpu_cycles
        # 李林娟3-18
        return a_n * (1 - R_n) * D_n * C / f_ue + (1 - a_n) * D_n * C / f_ue

    def _calc_transmission_rate(self, uav_pos, ue_pos, B_n):
        """计算UAV和UE之间的传输速率

        Args:
            uav_pos: UAV位置，形状为(M, 2)
            ue_pos: UE位置，形状为(N, 2)
            B_n: 终端遮挡状态，形状为(N,)

        Returns:
            float: 传输速率,对应公式3-4

        Notes:
            - gain_n: 信道增益,对应公式3-3
        """
        a_0 = self.uav_config.a_0
        a_0 = np.power(10, a_0 / 10)
        B = self.uav_config.B

        # 计算水平距离 - 使用广播机制
        d_horizontal = np.sqrt(
            np.sum((ue_pos[np.newaxis, :, :] - uav_pos[:, np.newaxis, :]) ** 2, axis=2)
        )
        # 计算实际距离（考虑高度差）
        d_n = np.sqrt(d_horizontal**2 + self.uav_config.H**2)

        P_up = self.uav_config.P_up
        P_nloss = self.uav_config.P_nloss
        P_nloss = np.power(10, P_nloss / 10)
        sigma_2 = self.uav_config.sigma_2
        sigma_2 = np.power(10, sigma_2 / 10)

        # 使用广播机制计算信道增益
        gain_n = a_0 / (d_n**2)  # 公式3-3
        # 使用广播机制计算传输速率
        r_n = B * np.log2(
            1
            + P_up
            * gain_n
            / (P_nloss * B_n[np.newaxis, :] + sigma_2 * (1 - B_n[np.newaxis, :]))
        )  # 公式3-4
        return r_n

    def _calc_relay_transmission(self, R_n, a_n, D_n, uav_pos, ue_pos, B_n):
        """计算传输时延

        Args:
            R_n: 终端任务卸载率，形状为(M,1)
            a_n: 当前选择的终端(one-hot编码)，形状为(M,N)
            D_n: 终端任务数据量，形状为(N,)
            uav_pos: UAV位置，形状为(M,2)
            ue_pos: UE位置，形状为(N,2)
            B_n: 终端遮挡状态，形状为(N,)

        Returns:
            float: 终端传输时延,对应公式3-7

        Notes:
            - r_n: 传输速率,对应公式3-4
        """
        r_n = self._calc_transmission_rate(uav_pos, ue_pos, B_n)  # (M,N)
        # 使用广播机制计算传输时延
        return a_n * R_n * D_n[np.newaxis, :] / r_n  # 公式3-7

    def _calc_relay_uav_computation(self, R_n, a_n, D_n):
        """计算UAV计算时延

        Args:
            R_n: 终端任务卸载率，形状为(M,1)
            a_n: 当前选择的终端(one-hot编码)，形状为(M,N)
            D_n: 终端任务数据量，形状为(N,)

        Returns:
            float: UAV计算时延,对应公式3-8
        """
        f_uav = self.uav_config.f_uav
        C = self.uav_config.cpu_cycles
        # 使用广播机制计算UAV计算时延
        return a_n * R_n * D_n[np.newaxis, :] * C / f_uav  # 公式3-8

    def _calc_energy_fly(self, v):
        """计算UAV飞行能耗

        Args:
            v: 飞行速度
            theta: 飞行角度

        Returns:
            E_fly: UAV飞行能耗，对应公式3-2
        """
        M_uav = self.uav_config.M_uav
        t_fly = self.uav_config.t_fly
        return 0.5 * M_uav * v**2 * t_fly  # 公式3-2

    def _calc_energy_ue_computation(self, T_n_local):
        """计算终端本地计算能耗

        Args:
            T_n_local: 终端本地计算时延

        Returns:
            E_n_local: 终端本地计算能耗，对应公式3-6
        """
        f_ue = self.uav_config.f_ue
        delta = self.uav_config.delta
        return delta * np.power(f_ue, 3) * T_n_local  # 公式3-6

    def _calc_energy_uav_computation(self, T_n_uav):
        """计算UAV计算能耗

        Args:
            T_n_uav: UAV计算时延

        Returns:
            E_n_compute: UAV计算能耗，对应公式3-9
        """
        f_uav = self.uav_config.f_uav
        k = self.uav_config.k
        return k * np.power(f_uav, 3) * T_n_uav  # 公式3-9

    def _calc_energy_transmission(self, T_n_trans):
        """计算传输能耗

        Args:
            T_n_trans: 传输时延

        Returns:
            E_n_trans: 传输能耗，对应公式3-10
        """
        delta = self.uav_config.delta
        f_ue = self.uav_config.f_ue
        return delta * np.power(f_ue, 3) * T_n_trans  # 公式3-10

    def _calc_relay(self, a_n, D_n, R_n, uav_pos, ue_pos, B_n):
        """计算UAV计算时延

        Args:
            a_n: 当前选择的终端(one-hot编码)
            D_n: 终端任务数据量
            R_n: 终端任务卸载率

        Returns:
            float: 传输时延,对应公式3-7
        """
        # r_n = self._calc_transmission_rate()
        # cpu_cycles = self.uav_config.cpu_cycles
        # f_uav = self.uav_config.f_uav
        # f_ue = self.uav_config.f_ue
        # R_n = (cpu_cycles * f_uav * r_n) \
        #       / (cpu_cycles * f_uav * r_n + cpu_cycles * f_ue * r_n + f_ue * f_uav)
        T_n_local = self._calc_relay_ue_computation(R_n, a_n, D_n)
        T_n_trans = self._calc_relay_transmission(R_n, a_n, D_n, uav_pos, ue_pos, B_n)
        T_n_uav = self._calc_relay_uav_computation(R_n, a_n, D_n)
        T_n = np.maximum(T_n_local, T_n_trans + T_n_uav)
        return T_n, T_n_local, T_n_trans, T_n_uav

    def _calc_energy(self, T_n_local, T_n_trans, T_n_uav, v):
        """计算各种能耗

        Args:
            T_n_local: 终端本地计算时延，形状为(N,)
            T_n_trans: 传输时延，形状为(M, N)
            T_n_uav: UAV计算时延，形状为(M, N)
            v: 每个UAV的飞行速度，形状为(M,)

        Returns:
            E_n: 终端总能耗，形状为(N,)
            E_uav: UAV总能耗，形状为(M,)
            E_fly: 飞行能耗，形状为(M,)
        """
        # 计算各种能耗
        E_n_local = self._calc_energy_ue_computation(T_n_local)  # (N,)
        E_n_trans = self._calc_energy_transmission(T_n_trans)  # (M, N)
        E_n_uav = self._calc_energy_uav_computation(T_n_uav)  # (M, N)
        E_fly = self._calc_energy_fly(v)  # (M,)

        # 计算终端总能耗（不考虑UAV计算能耗，为了鼓励卸载）
        E_n = E_n_local + np.sum(E_n_trans, axis=0)  # (N,)

        # 计算UAV总能耗
        E_uav = np.sum(E_n_uav, axis=1)  # (M,)

        return E_n, E_uav, E_fly

    def _transfer_a_n(self, action):
        a_n = np.zeros((self.uav_config.M, self.uav_config.N))
        for i in range(self.uav_config.M):
            a_n[i, action["m"][i]] = 1
        return a_n

    def step(self, action):
        # 将动作重塑为每个UAV的动作
        action = action.reshape(self.uav_config.M, 4)

        dict_action = {
            "m": action[:, 0].astype(int),  # 每个UAV选择的终端序号
            "theta": action[:, 1],  # 每个UAV的飞行角度
            "v": action[:, 2],  # 每个UAV的飞行速度
            "R_n": action[:, 3],  # 每个UAV的卸载率
        }

        # 根据动作对环境进行处理
        return self._step_impl(dict_action)

    def _update_tasks(self, D_n, T_n, total_offload_ratios):
        """更新任务量和队列
        Args:
            D_n: 当前时隙的任务数据量
            T_n: 当前时隙的任务时延
            total_offload_ratios: 每个终端的总卸载率
        Returns:
            new_D_n: 更新后的任务数据量
            new_Q_t: 更新后的任务队列
            unfinished: 未完成的任务量
        """
        # 初始化未完成任务量数组
        unfinished = np.zeros_like(D_n)

        # 对每个终端进行处理
        for j in range(self.uav_config.N):
            # 判断是否能在当前时隙内处理完
            can_process = T_n[j] <= self.uav_config.t_com

            if not can_process:
                # 计算未完成任务量
                unfinished[j] = D_n[j] * (1 - self.uav_config.t_com / T_n[j])

        # 生成新的随机任务
        # new_tasks = np.random.uniform(
        #     self.uav_config.D_total / 100 / self.uav_config.N,
        #     self.uav_config.D_total / 200 / self.uav_config.N,
        #     size=self.uav_config.N,
        # )
        new_tasks = np.random.uniform(
            self.uav_config.D_total / 100,
            self.uav_config.D_total / 200,
            size=self.uav_config.N,
        )
        # 更新任务队列
        new_Q_t = self.Q_t + np.sum(unfinished)

        # 更新总任务量：未完成任务 + 新任务
        new_D_n = unfinished + new_tasks

        return new_D_n, new_Q_t, unfinished

    def _calc_collaborative_ratios(self, a_n, R_n):
        """计算每个终端的总卸载率和任务分配比例

        Args:
            a_n: 每个UAV选择的终端(one-hot编码)，形状为(M, N)
            R_n: 每个UAV的卸载率，形状为(M,)

        Returns:
            total_offload_ratios: 每个终端的总卸载率，形状为(N,)
            uav_task_ratios: 每个UAV对每个终端的任务分配比例，形状为(M, N)
        """
        # 计算每个终端的总卸载率
        total_offload_ratios = np.zeros(self.uav_config.N)
        for i in range(self.uav_config.M):
            for j in range(self.uav_config.N):
                if a_n[i, j] == 1:
                    total_offload_ratios[j] += R_n[i]

        # 计算每个UAV对每个终端的任务分配比例
        uav_task_ratios = np.zeros((self.uav_config.M, self.uav_config.N))
        for i in range(self.uav_config.M):
            for j in range(self.uav_config.N):
                if a_n[i, j] == 1 and total_offload_ratios[j] > 0:
                    # 按比例分配卸载任务
                    uav_task_ratios[i, j] = R_n[i] / total_offload_ratios[j]

        return total_offload_ratios, uav_task_ratios

    def _calc_collaborative_task(
        self,
        a_n,
        D_n,
        R_n,
        uav_pos,
        ue_pos,
        B_n,
        v,
        total_offload_ratios,
        uav_task_ratios,
    ):
        """计算协作任务的时延和能耗

        Args:
            a_n: 每个UAV选择的终端(one-hot编码)，形状为(M, N)
            D_n: 终端任务数据量，形状为(N,)
            R_n: 每个UAV的卸载率，形状为(M,)
            uav_pos: 每个UAV的位置，形状为(M, 2)
            ue_pos: 终端位置，形状为(N, 2)
            B_n: 终端遮挡状态，形状为(N,)
            v: 每个UAV的飞行速度，形状为(M,)
            total_offload_ratios: 每个终端的总卸载率，形状为(N,)
            uav_task_ratios: 每个UAV对每个终端的任务分配比例，形状为(M, N)

        Returns:
            total_delay: 总时延
            total_energy: 总能耗
            collaborative_T_n: 每个终端的协作时延，形状为(N,)
            collaborative_E_n: 每个终端的协作能耗，形状为(N,)
            all_E_uav_effective: 每个UAV的有效计算能耗，形状为(M,)
        """
        # 初始化结果数组
        collaborative_T_n = np.zeros(self.uav_config.N)
        collaborative_E_n = np.zeros(self.uav_config.N)
        all_E_uav_effective = np.zeros(self.uav_config.M)

        # 计算每个终端是否被选中
        selected_mask = np.any(a_n, axis=0)  # (N,)
        unselected_mask = ~selected_mask

        # 处理未被选中的终端（完全本地计算）
        if np.any(unselected_mask):
            T_local_unselected = self._calc_relay_ue_computation(
                np.zeros_like(total_offload_ratios[unselected_mask]),
                np.ones_like(total_offload_ratios[unselected_mask]),
                D_n[unselected_mask],
            )
            E_local_unselected = self._calc_energy_ue_computation(T_local_unselected)
            collaborative_T_n[unselected_mask] = T_local_unselected
            collaborative_E_n[unselected_mask] = E_local_unselected

        # 处理被选中的终端
        if np.any(selected_mask):
            # 计算本地计算部分
            local_mask = selected_mask & (total_offload_ratios < 1)
            if np.any(local_mask):
                T_local_selected = self._calc_relay_ue_computation(
                    total_offload_ratios[local_mask],
                    np.ones_like(total_offload_ratios[local_mask]),
                    D_n[local_mask],
                )
                E_local_selected = self._calc_energy_ue_computation(T_local_selected)
                collaborative_T_n[local_mask] = T_local_selected
                collaborative_E_n[local_mask] = E_local_selected

            # 计算UAV卸载部分
            # 调整有效卸载率
            effective_R_n = np.where(
                total_offload_ratios > 1, uav_task_ratios, R_n[:, np.newaxis]
            )

            # 计算传输时延和UAV计算时延
            T_trans = np.zeros((self.uav_config.M, self.uav_config.N))
            T_uav = np.zeros((self.uav_config.M, self.uav_config.N))

            # 对每个UAV计算传输时延
            for i in range(self.uav_config.M):
                T_trans[i] = self._calc_relay_transmission(
                    effective_R_n[i : i + 1],  # 保持(M,1)维度
                    a_n[i : i + 1],  # 保持(M,N)维度
                    D_n,
                    uav_pos[i : i + 1],  # 保持(M,2)维度
                    ue_pos,
                    B_n,
                )
                T_uav[i] = self._calc_relay_uav_computation(
                    effective_R_n[i : i + 1],  # 保持(M,1)维度
                    a_n[i : i + 1],  # 保持(M,N)维度
                    D_n,
                )

            # 计算传输能耗
            E_trans = self._calc_energy_transmission(T_trans)
            E_uav = self._calc_energy_uav_computation(T_uav)

            # 更新UAV有效计算能耗
            all_E_uav_effective = np.sum(E_uav * a_n, axis=1)

            # 计算每个终端的最大UAV时延
            max_uav_delay = np.max((T_trans + T_uav) * a_n, axis=0)

            # 根据情况计算总时延
            multi_uav_mask = np.sum(a_n, axis=0) > 1
            full_offload_mask = total_offload_ratios >= 1

            # 更新协作时延
            collaborative_T_n[selected_mask] = np.where(
                multi_uav_mask & full_offload_mask,
                max_uav_delay,
                np.maximum(collaborative_T_n, max_uav_delay),
            )[selected_mask]

            # 更新协作能耗
            collaborative_E_n[selected_mask] += np.sum(E_trans * a_n, axis=0)[
                selected_mask
            ]

        # 计算总时延和能耗
        total_delay = np.sum(collaborative_T_n * a_n)
        total_energy = np.sum(collaborative_E_n * a_n)

        return (
            total_delay,
            total_energy,
            collaborative_T_n,
            collaborative_E_n,
            all_E_uav_effective,
        )

    def _step_impl(self, action):
        done = False
        cost = 0
        truncated = False

        # 从状态向量中提取各个元素
        # UAV剩余电量(M) + UAV位置(M*2) + 剩余数据量(1) + 终端位置(N*2) + 终端数据量(N) + 终端遮挡状态(N)
        idx = 0
        E_b = self.state[idx : idx + self.uav_config.M].reshape(
            self.uav_config.M, 1
        )  # 每个UAV的电池容量
        idx += self.uav_config.M

        uav_pos = self.state[idx : idx + self.uav_config.M * 2].reshape(
            self.uav_config.M, 2
        )  # 每个UAV的位置
        idx += self.uav_config.M * 2

        D_r = self.state[idx]  # 总剩余数据量
        idx += 1

        ue_positions = self.state[idx : idx + 2 * self.uav_config.N].reshape(
            self.uav_config.N, 2
        )  # 终端位置
        idx += 2 * self.uav_config.N

        D_n = self.state[idx : idx + self.uav_config.N]  # 终端任务数据量
        idx += self.uav_config.N

        block_flags = self.state[idx : idx + self.uav_config.N]  # 终端遮挡状态

        # 计算前更新状态
        a_n = self._transfer_a_n(action)  # 每个UAV选择的终端，形状为(M, N)

        # 计算每个UAV的新位置
        dx = action["v"].reshape(-1, 1) * np.cos(action["theta"].reshape(-1, 1))
        dy = action["v"].reshape(-1, 1) * np.sin(action["theta"].reshape(-1, 1))
        new_u = uav_pos + np.hstack((dx, dy))
        new_u = np.clip(new_u, [0, 0], [self.uav_config.L, self.uav_config.W])

        # 计算每个UAV的计算时延和能耗
        T_uav_all = []
        E_uav_all = []
        E_fly_all = []

        for i in range(self.uav_config.M):
            T_uav = self._calc_relay_uav_computation(
                action["R_n"][i : i + 1],  # 保持(M,1)维度
                a_n[i : i + 1],  # 保持(M,N)维度
                D_n,
            )
            E_uav = self._calc_energy_uav_computation(T_uav)
            E_fly = self._calc_energy_fly(action["v"][i : i + 1])  # 保持(M,1)维度

            T_uav_all.append(T_uav)
            E_uav_all.append(E_uav)
            E_fly_all.append(E_fly)

        # 默认值
        total_delay = 0
        total_energy = 0

        # 检查是否完成所有任务
        if D_r <= 0:
            done = True
            reward = 0
            new_D_r = D_r
            new_Q_t = self.Q_t
            new_E_b = E_b
        # 检查是否有UAV飞出区域
        elif np.any(
            (new_u < 0) | (new_u > np.array([self.uav_config.L, self.uav_config.W]))
        ):
            reward = 0
            new_u = np.clip(new_u, [0, 0], [self.uav_config.L, self.uav_config.W])
            self.failed_count += 1
            print("飞出了边界")
            truncated = True
            new_D_r = D_r
            new_Q_t = self.Q_t
            new_E_b = E_b
        # 检查是否有UAV电池能量不足以飞行
        elif np.any(E_b - np.array(E_fly_all) < 0):
            reward = 0
            done = True
            print("电池能量不足以飞行")
            self.failed_count += 1
            truncated = True
            new_D_r = D_r
            new_Q_t = self.Q_t
            new_E_b = E_b
        # 检查是否有UAV电池能量不足以计算
        elif np.any(
            E_b
            - np.array(E_fly_all)
            - np.sum(a_n * np.array(E_uav_all), axis=1, keepdims=True)
            < 0
        ):
            print("电池能量不足以计算")
            reward = 0
            done = True
            self.failed_count += 1
            truncated = True
            new_D_r = D_r
            new_Q_t = self.Q_t
            new_E_b = E_b - np.array(E_fly_all)
        else:
            # 计算每个终端的总卸载率和任务分配比例
            total_offload_ratios, uav_task_ratios = self._calc_collaborative_ratios(
                a_n, action["R_n"]
            )

            # 计算协作任务的时延和能耗
            (
                total_delay,
                total_energy,
                collaborative_T_n,
                all_E_n,
                all_E_uav_effective,
            ) = self._calc_collaborative_task(
                a_n,
                D_n,
                action["R_n"],
                new_u,
                ue_positions,
                block_flags,
                action["v"],
                total_offload_ratios,
                uav_task_ratios,
            )

            new_D_n, new_Q_t, unfinished = self._update_tasks(
                D_n, collaborative_T_n, total_offload_ratios
            )

            # 计算Lyapunov漂移
            L_t = 1 / 2 * (new_Q_t / 1e4)
            L_t_1 = 1 / 2 * (self.Q_t / 1e4)

            delta = L_t - L_t_1

            # 计算奖励：最小化李雅普诺夫代价函数漂移
            reward = -(
                delta
                + self.uav_config.V
                * (
                    self.uav_config.w_T * total_delay
                    + self.uav_config.w_E * total_energy
                )
            )

            cost = reward
            self.sum_cost += cost

        if not done:
            # 计算结束后更新
            try:
                # 计算所有终端处理的总数据量 - 考虑协作处理
                processed_data = 0
                for j in range(self.uav_config.N):
                    if total_offload_ratios[j] > 0:  # 此终端有UAV选择
                        processed_data += D_n[j] - unfinished[j]
                new_D_r = D_r - processed_data
            except UnboundLocalError:
                new_D_r = D_r

            # 更新每个UAV的电池容量
            new_E_b = np.copy(E_b)
            for i in range(self.uav_config.M):
                new_E_b[i] -= E_fly_all[i]
                new_E_b[i] -= all_E_uav_effective[i]  # 使用有效计算能耗

            # 更新终端位置
            movement = np.random.uniform(-5, 5, size=(self.uav_config.N, 2))
            new_P = ue_positions + movement
            new_P = np.clip(new_P, [0, 0], [self.uav_config.L, self.uav_config.W])

            # 更新终端遮挡状态
            zero_mask = (block_flags == 0) & (
                np.random.random(self.uav_config.N) < 0.50
            )
            one_mask = (block_flags == 1) & (np.random.random(self.uav_config.N) < 0.5)
            new_block_flag = block_flags.copy()
            new_block_flag[zero_mask | one_mask] = (
                1 - new_block_flag[zero_mask | one_mask]
            )

            # 更新状态列表
            state_list = []
            state_list.extend(new_E_b.flatten().tolist())  # 每个UAV的电池容量
            state_list.extend(new_u.flatten().tolist())  # 每个UAV的位置
            state_list.append(new_D_r)  # 总剩余数据量
            state_list.extend(new_P.flatten().tolist())  # 终端位置
            state_list.extend(new_D_n.tolist())  # 终端任务量
            state_list.extend(new_block_flag.tolist())  # 终端遮挡状态

            self.state = np.array(state_list, dtype=np.float32)
            self.Q_t = new_Q_t

            self.current_step += 1
            done = self.current_step >= self.max_steps

        info = {
            "cost": -self.sum_cost / (self.current_step - self.failed_count)
            if self.current_step > self.failed_count
            else 0,
            "current_cost": cost,
            "truncated": truncated,
            "Q_t": self.Q_t,
            "D_r": new_D_r,
            "total_delay": total_delay,
            "total_energy": total_energy,
            "E_b": np.sum(new_E_b.tolist())
            if isinstance(new_E_b, np.ndarray)
            else new_E_b,  # 添加每个UAV的电池剩余电量
            "offload_ratio": action["R_n"].tolist()
            if isinstance(action["R_n"], np.ndarray)
            else action["R_n"],  # 添加每个UAV的卸载比例
        }

        return self.state.copy(), reward, done, info
