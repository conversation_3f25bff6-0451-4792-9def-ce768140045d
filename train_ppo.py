from stable_baselines3 import PPO
from stable_baselines3.common.vec_env import DummyVecEnv, VecNormalize
from stable_baselines3.common.callbacks import CheckpointCallback, EvalCallback
from stable_baselines3.common.type_aliases import RolloutReturn
from stable_baselines3.common.utils import should_collect_more_steps, obs_as_tensor
from stable_baselines3.common.torch_layers import BaseFeaturesExtractor
import torch as th
import gym

from policy import UAVEnvExtrator
from stable_baselines3.common.monitor import Monitor
import numpy as np
import matplotlib.pyplot as plt
import os
import nni
import argparse

parser = argparse.ArgumentParser()
parser.add_argument("--nni", action="store_true", help="use nni")
parser.add_argument("--v", type=float, default=0, help="trade-off between target and lyapunov drift")
parser.add_argument("--M", type=int, default=1, help="number of UAVs")
parser.add_argument("--N", type=int, default=2, help="number of UEs")

args = parser.parse_args()

if args.v == 0:
    from envs.no_lyapunov_envs import UAVEnv
else:
    from envs.lyapunov_envs import UAVEnv

if args.nni:
    optimized_params = nni.get_next_parameter()
# 默认参数设置
params = {
    'learning_rate': 1e-3,
    'n_steps': 2048,
    'batch_size': 64,
    'gae_lambda': 0.95,
    'clip_range': 0.2,
    'n_epochs': 10
}

if args.nni:
    params.update(optimized_params)
print(params)

def make_env(rank):
    def _init():
        if args.v != 0:
            env = UAVEnv(v=args.v, M=args.M, N=args.N)
        else:
            env = UAVEnv(M=args.M, N=args.N)
        env = Monitor(env)
        return env
    return _init

# 创建自定义EvalCallback类
class CustomEvalCallback(EvalCallback):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.costs = []
        self.rewards = []  # 用于存储每个episode的reward
        self.delays = []   # 用于存储每个episode的时延
        self.energies = [] # 用于存储每个episode的能耗
        self.Qts = []
        self.Ebs = []
        self.current_episode_reward = 0  # 当前episode的累积reward
        self.current_episode_delay = 0   # 当前episode的累积时延
        self.current_episode_energy = 0  # 当前episode的累积能耗
        self.current_episode_Qt = 0
        self.current_episode_Eb = 0
        
    def _on_step(self) -> bool:
        # 检查是否有episode结束
        dones = self.locals.get("dones", [])
        rewards = np.array([self.locals.get('infos', [])[0]['current_cost']], dtype=np.float32)
        
        # 获取时延和能耗信息
        infos = self.locals.get('infos', [{}])
        if infos and len(infos) > 0:
            delay = infos[0].get('total_delay', 0)
            energy = infos[0].get('total_energy', 0)
            Q_t = infos[0].get('Q_t', 0)
            E_b = infos[0].get('E_b', 0)
        else:
            delay = 0
            energy = 0
            Q_t = 0
            E_b = 0
        
        # 更新当前episode的累积指标
        for done, reward in zip(dones, rewards):
            self.current_episode_reward += reward
            self.current_episode_delay += delay
            self.current_episode_energy += energy
            self.current_episode_Qt += Q_t
            self.current_episode_Eb += E_b
            
            if done:
                # episode结束，记录指标
                self.rewards.append(self.current_episode_reward)
                self.delays.append(self.current_episode_delay)
                self.energies.append(self.current_episode_energy)
                self.Qts.append(self.current_episode_Qt)
                self.Ebs.append(self.current_episode_Eb)
                self.current_episode_reward = 0
                self.current_episode_delay = 0
                self.current_episode_energy = 0
                self.current_episode_Qt = 0
                self.current_episode_Eb = 0
                
                # 保存指标数据
                np.save(os.path.join(self.log_path, 'episode_rewards.npy'), np.array(self.rewards))
                np.save(os.path.join(self.log_path, 'episode_delays.npy'), np.array(self.delays))
                np.save(os.path.join(self.log_path, 'episode_energies.npy'), np.array(self.energies))
                np.save(os.path.join(self.log_path, 'episode_queue_length.npy'), np.array(self.Qts))
                np.save(os.path.join(self.log_path, 'episode_battery_level.npy'), np.array(self.Ebs))
                
                # 绘制奖励图表
                plt.figure(figsize=(10, 6))
                plt.plot(range(1, len(self.rewards) + 1), self.rewards)
                plt.xlabel('Episode')
                plt.ylabel('Episode Reward')
                plt.title('Episode Rewards over Time')
                plt.grid(True)
                plt.savefig(os.path.join(self.log_path, 'episode_rewards.png'))
                plt.close()
                
                # 绘制时延图表
                plt.figure(figsize=(10, 6))
                plt.plot(range(1, len(self.delays) + 1), self.delays)
                plt.xlabel('Episode')
                plt.ylabel('Episode Delay')
                plt.title('Episode Delays over Time')
                plt.grid(True)
                plt.savefig(os.path.join(self.log_path, 'episode_delays.png'))
                plt.close()
                
                # 绘制能耗图表
                plt.figure(figsize=(10, 6))
                plt.plot(range(1, len(self.energies) + 1), self.energies)
                plt.xlabel('Episode')
                plt.ylabel('Episode Energy')
                plt.title('Episode Energies over Time')
                plt.grid(True)
                plt.savefig(os.path.join(self.log_path, 'episode_energies.png'))
                plt.close()
                
                # 绘制Qt图表
                plt.figure(figsize=(10, 6))
                plt.plot(range(1, len(self.Qts) + 1), self.Qts)
                plt.xlabel('Episode')
                plt.ylabel('Queue Length')
                plt.title('Task Queue Length over Time')
                plt.grid(True)
                plt.savefig(os.path.join(self.log_path, 'episode_queue_length.png'))
                plt.close()

                # 绘制Eb图表
                plt.figure(figsize=(10, 6))
                plt.plot(range(1, len(self.Ebs) + 1), self.Ebs)
                plt.xlabel('Episode')
                plt.ylabel('Battery Level')
                plt.title('Battery Level over Time')
                plt.grid(True)
                plt.savefig(os.path.join(self.log_path, 'episode_battery_level.png'))
                plt.close()
                
                # 绘制综合图表
                plt.figure(figsize=(12, 8))
                plt.subplot(5, 1, 1)
                plt.plot(range(1, len(self.rewards) + 1), self.rewards)
                plt.ylabel('Reward')
                plt.grid(True)
                
                plt.subplot(5, 1, 2)
                plt.plot(range(1, len(self.delays) + 1), self.delays)
                plt.ylabel('Delay')
                plt.grid(True)
                
                plt.subplot(5, 1, 3)
                plt.plot(range(1, len(self.energies) + 1), self.energies)
                plt.ylabel('Energy')
                plt.grid(True)

                plt.subplot(5, 1, 4)
                plt.plot(range(1, len(self.Qts) + 1), self.Qts)
                plt.ylabel('Queue Length')
                plt.grid(True)

                plt.subplot(5, 1, 5)
                plt.plot(range(1, len(self.Ebs) + 1), self.Ebs)
                plt.xlabel('Episode')
                plt.ylabel('Battery Level')
                plt.grid(True)
                
                plt.tight_layout()
                plt.savefig(os.path.join(self.log_path, 'episode_metrics.png'))
                plt.close()
        
        if self.eval_freq > 0 and self.n_calls % self.eval_freq == 0:
            # 执行原有的评估逻辑
            result = super()._on_step()
            
            # 获取最近一次评估的info
            if len(self.evaluations_timesteps) > 0:
                # 从self.locals中获取评估信息
                eval_infos = self.locals.get("infos", [])
                episode_costs = []
                episode_delays = []
                episode_energies = []
                
                # 获取评估结果
                for info in eval_infos:
                    if "cost" in info:
                        episode_costs.append(info["cost"])
                    if "total_delay" in info:
                        episode_delays.append(info["total_delay"])
                    if "total_energy" in info:
                        episode_energies.append(info["total_energy"])
                
                # 计算并记录平均指标
                if episode_costs:
                    mean_cost = np.mean(episode_costs)
                    self.costs.append(mean_cost)
                    self.logger.record("eval/mean_cost", mean_cost)
                
                if episode_delays:
                    mean_delay = np.mean(episode_delays)
                    self.logger.record("eval/mean_delay", mean_delay)
                    
                if episode_energies:
                    mean_energy = np.mean(episode_energies)
                    self.logger.record("eval/mean_energy", mean_energy)
                
                self.logger.dump(self.n_calls)  # 确保日志被打印
            
            return result
            
        return True

class CustomPPO(PPO):
    def collect_rollouts(
        self,
        env,
        callback,
        rollout_buffer,
        n_rollout_steps: int
    ) -> bool:
        """
        重写collect_rollouts方法，在收集经验时检查truncated标志
        """
        assert self._last_obs is not None, "No previous observation was provided"
        n_steps = 0
        rollout_buffer.reset()
        
        callback.on_rollout_start()
        
        # 记录成功收集的步骤数
        collected_step_count = 0
        max_steps = n_rollout_steps * 2  # 设置收集步骤的上限以避免无限循环
        
        while n_steps < n_rollout_steps and collected_step_count < max_steps:
            if self.use_sde and self.sde_sample_freq > 0 and n_steps % self.sde_sample_freq == 0:
                # Sample a new noise matrix
                self.policy.reset_noise(env.num_envs)
                
            with th.no_grad():
                # Convert to pytorch tensor or to TensorDict
                obs_tensor = obs_as_tensor(self._last_obs, self.device)
                actions, values, log_probs = self.policy.forward(obs_tensor)
            actions = actions.cpu().numpy()
            
            # Rescale and perform action
            clipped_actions = actions
            # Clip the actions to avoid out of bound error
            if isinstance(self.action_space, gym.spaces.Box):
                clipped_actions = np.clip(actions, self.action_space.low, self.action_space.high)
            
            try:
                new_obs, rewards, dones, infos = env.step(clipped_actions)
            except Exception as e:
                print(f"环境step发生错误: {e}")
                # 如果环境抛出错误，重置环境并继续
                self._last_obs = env.reset()
                self._last_episode_starts = np.ones((env.num_envs,), dtype=bool)
                continue
            
            self.num_timesteps += env.num_envs
            collected_step_count += 1
            
            # Give access to local variables
            callback.update_locals(locals())
            if callback.on_step() is False:
                return False
                
            self._update_info_buffer(infos, dones)
            
            # 检查环境是否因为达到步数限制而被截断
            skip_store = False
            battery_error = False
            for idx, info in enumerate(infos):
                if info.get("truncated", False):
                    skip_store = True
                # 检查是否有特定错误信息
                if "error" in info and "battery" in info["error"].lower():
                    battery_error = True
                    print(f"检测到电池能量不足: {info['error']}")
            
            if battery_error:
                # 如果是电池能量不足，重置环境
                print("由于电池能量不足，重置环境...")
                self._last_obs = env.reset()
                self._last_episode_starts = np.ones((env.num_envs,), dtype=bool)
                continue
                    
            if not skip_store:
                # 只有在不截断的情况下才存储经验
                rollout_buffer.add(
                    self._last_obs, actions, rewards, self._last_episode_starts, values, log_probs
                )
                n_steps += 1  # 只有在成功添加经验时才计数
                
            self._last_obs = new_obs
            self._last_episode_starts = dones
        
        # 确保收集足够的数据
        if n_steps < n_rollout_steps * 0.5:  # 如果收集的数据太少
            print(f"警告: 只收集到 {n_steps}/{n_rollout_steps} 步的有效数据。执行额外的数据收集...")
            # 强制设置rollout_buffer的full标志为True，以便训练可以继续
            rollout_buffer.full = True
            
        with th.no_grad():
            # Compute value for the last timestep
            obs_tensor = obs_as_tensor(new_obs, self.device)
            _, values, _ = self.policy.forward(obs_tensor)
        
        rollout_buffer.compute_returns_and_advantage(last_values=values, dones=dones)
        
        callback.on_rollout_end()
        
        return True

if __name__ == "__main__":
    # 创建环境
    n_envs = 1
    env = DummyVecEnv([make_env(i) for i in range(n_envs)])
    env = VecNormalize(env, norm_obs=True, norm_reward=True)
    
    # 创建评估环境
    eval_env = DummyVecEnv([make_env(i) for i in range(1)])
    eval_env = VecNormalize(eval_env, norm_obs=True, norm_reward=True)
    eval_env.obs_rms = env.obs_rms
    eval_env.ret_rms = env.ret_rms
    
    # 创建模型
    policy_kwargs = dict(
        features_extractor_class=UAVEnvExtrator,
        features_extractor_kwargs=dict(features_dim=128),
        net_arch=[dict(pi=[64, 64], vf=[64, 64])]
    )
    
    # 创建PPO模型
    model = CustomPPO(
        "MlpPolicy",
        env,
        learning_rate=params['learning_rate'],
        n_steps=params['n_steps'],
        batch_size=params['batch_size'],
        gae_lambda=params['gae_lambda'],
        clip_range=params['clip_range'],
        n_epochs=params['n_epochs'],
        gamma=0.99,
        policy_kwargs=policy_kwargs,
        verbose=1
    )
    
    # 创建日志和模型文件夹
    if not os.path.exists("./logs/ppo/v={}M={}N={}/".format(args.v, args.M, args.N)):
        os.makedirs("./logs/ppo/v={}M={}N={}/".format(args.v, args.M, args.N))
    if not os.path.exists("./logs/ppo/v={}M={}N={}/evaluations/".format(args.v, args.M, args.N)):
        os.makedirs("./logs/ppo/v={}M={}N={}/evaluations/".format(args.v, args.M, args.N))
    if not os.path.exists("./best_model/ppo/v={}M={}N={}/".format(args.v, args.M, args.N)):
        os.makedirs("./best_model/ppo/v={}M={}N={}/".format(args.v, args.M, args.N))
    
    # 创建回调函数
    checkpoint_callback = CheckpointCallback(
        save_freq=10000,
        save_path="./logs/ppo/v={}M={}N={}/".format(args.v, args.M, args.N),
        name_prefix="ppo_model"
    )
    
    # 创建评估回调
    eval_callback = CustomEvalCallback(
        eval_env,
        best_model_save_path="./best_model/ppo/v={}M={}N={}/".format(args.v, args.M, args.N),
        log_path="./logs/ppo/v={}M={}N={}/".format(args.v, args.M, args.N),
        eval_freq=10000,
        deterministic=True,
        render=False,
    )
    
    # 训练模型
    model.learn(
        total_timesteps=50000,
        callback=[checkpoint_callback, eval_callback],
        log_interval=10000
    )
    
    # 报告训练过程中的最终cost作为优化目标
    if args.nni:
        nni.report_final_result(np.mean(eval_callback.costs))