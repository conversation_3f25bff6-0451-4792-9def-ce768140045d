## 状态空间定义

1. 终端在各时刻位置

$P\left(t\right)=\left\{p_1\left(t\right),...,p_n\left(t\right)\right\}$ 

`Box(low=np.array([0, 0]), high=np.array([L, W]), shape=(n, 2), dtype=np.float32)`

2. 终端随机产生的任务大小

$D(t)=\left\{d_1(t),...,d_n(t)\right\}$

取值：
$\sum_{t=1}^{T}\sum_{n=1}^{N}\alpha_n\left(t\right)D_n\left(t\right)=D      （3.13j）$

这个约束似乎是想让前T个时隙处理的总数据量相同，但在程序中很难实现

`Box(low=0, high=D, shape=(n,), dtype=np.float32)` 
暂时先这么定义，还缺个约束

3. 终端是否被障碍物阻挡

$B_n\left(t\right)$ 表示第n个终端在t时刻是否被障碍物阻挡,取值为0或1

`MultiBinary(n)` 表示n个二进制变量的空间,每个变量取值为0或1

4. UAV（服务器）位置

$u\left(t\right)$

`Box(low=np.array([0, 0]), high=np.array([L, W]), shape=(n, 2), dtype=np.float32)`

5. 系统在时隙t 所需完成的剩余任务大小

$D_r (t)$

`Box(low=0, high=D, shape=(n,), dtype=np.float32)` 暂时先这么定义

6. UAV在时隙t 所剩余的电池容量

$E_b (t)$

`Box(low=0, high=E_b_max, shape=(n,), dtype=np.float32)`

## 动作空间定义

1. UAV在时隙t选择服务的移动终端 m(t)

取值:
- m(t)∈[0,n] 表示UAV在时隙t选择服务的移动终端
- 若m(t)=0, m'=1
- 若m(t)≠0, m'=⌈m(t)⌉, 其中⌈.⌉表示向上取整

`spaces.Discrete(n+1)` 表示从0到n的离散动作空间

2. θ(t) 表示UAV偏航角度，取值范围为[-π,π]

`spaces.Box(low=0, high=2 * np.pi, shape=(1,), dtype=np.float32)` 表示一个连续的角度空间

3. ν(t) 表示UAV 飞行速度，取值范围为[0,vmax]

`spaces.Box(low=0, high=vmax, shape=(1,), dtype=np.float32)` 表示一个连续的速度空间

4. R_n (t) 表示移动终端n 时隙t时任务的卸载率

取值范围为[0,1]，表示卸载任务的比例

`spaces.Box(low=0, high=1, shape=(n,), dtype=np.float32)` 


## 奖励函数定义
$r_t=-∑_{n=1}^N(η(t)Tn(t)+(1-η(t))E_{total} (t))$

def calc_reward(self, T_n, E_total, eta=0.5):
    """计算奖励值
    Args:
        T_n: 各终端的时延列表
        E_total: 总能耗
        eta: 时延和能耗的权重系数
    Returns:
        reward: 奖励值
    """
    reward = -np.sum(eta * T_n + (1 - eta) * E_total)
    return reward




{
    "learning_rate": 0.00014445165798993015,
    "batch_size": 256,
    "buffer_size": 1000,
    "tau": 0.009561110047002648,
    "learning_starts": 10000
}