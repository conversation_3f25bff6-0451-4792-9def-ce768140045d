#!/usr/bin/env python3
"""
Test script to demonstrate A2C v-value comparison functionality.
This script directly calls the plot_algorithm_v_comparison function.
"""

from flexible_reward_plotter import plot_algorithm_v_comparison

def main():
    print("Testing A2C v-value comparison...")
    
    # Test 1: Basic comparison of v=0.0 and v=1.0 for A2C with M=1N=2
    print("\n1. Comparing A2C with v=0.0 and v=1.0 (M=1N=2)")
    plot_algorithm_v_comparison(
        algorithm="a2c",
        base_config="M=1N=2",
        v_values=[0.0, 1.0],
        show_stats=True
    )
    
    # Test 2: Same comparison but with smoothing
    print("\n2. Same comparison with smoothing (window=10)")
    plot_algorithm_v_comparison(
        algorithm="a2c",
        base_config="M=1N=2",
        v_values=[0.0, 1.0],
        smooth_window=10,
        show_stats=True
    )
    
    # Test 3: Try different configuration if available
    print("\n3. Comparing A2C with v=0.0 and v=1.0 (M=1N=4)")
    plot_algorithm_v_comparison(
        algorithm="a2c",
        base_config="M=1N=4",
        v_values=[0.0, 1.0],
        show_stats=True
    )

if __name__ == "__main__":
    main()
