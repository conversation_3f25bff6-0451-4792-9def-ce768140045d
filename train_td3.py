import argparse
import os

import matplotlib.pyplot as plt
import nni
import numpy as np
from stable_baselines3 import TD3
from stable_baselines3.common.callbacks import Checkpoint<PERSON>allback, EvalCallback
from stable_baselines3.common.monitor import Monitor
from stable_baselines3.common.noise import NormalActionNoise
from stable_baselines3.common.type_aliases import RolloutReturn
from stable_baselines3.common.utils import should_collect_more_steps
from stable_baselines3.common.vec_env import DummyVecEnv, VecNormalize

from policy import UAVEnvExtrator

parser = argparse.ArgumentParser()
parser.add_argument("--nni", action="store_true", help="use nni")
parser.add_argument(
    "--v", type=float, default=0, help="trade-off between target and lyapunov drift"
)
parser.add_argument("--M", type=int, default=1, help="number of UAVs")
parser.add_argument("--N", type=int, default=2, help="number of UEs")

args = parser.parse_args()

if args.v == 0:
    from envs.no_lyapunov_envs import UAVEnv
else:
    from envs.lyapunov_envs import UAVEnv

if args.nni:
    optimized_params = nni.get_next_parameter()
# 默认参数设置
params = {
    "learning_rate": 1e-4,
    "batch_size": 128,
    "buffer_size": int(1e4),
    "tau": 0.02,
    "learning_starts": 1e3,
    "policy_delay": 2,  # TD3特有参数：策略更新延迟
    "target_policy_noise": 0.2,  # TD3特有参数：目标策略噪声
    "target_noise_clip": 0.5,  # TD3特有参数：目标噪声裁剪
}

if args.nni:
    params.update(optimized_params)
print(params)


def make_env(rank):
    def _init():
        if args.v != 0:
            env = UAVEnv(v=args.v, M=args.M, N=args.N)
        else:
            env = UAVEnv(M=args.M, N=args.N)
        env = Monitor(env)
        return env

    return _init


# 创建自定义EvalCallback类
class CustomEvalCallback(EvalCallback):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.costs = []
        self.rewards = []  # 用于存储每个episode的reward
        self.delays = []  # 用于存储每个episode的时延
        self.energies = []  # 用于存储每个episode的能耗
        self.Qts = []
        self.Ebs = []
        self.current_episode_reward = 0  # 当前episode的累积reward
        self.current_episode_delay = 0  # 当前episode的累积时延
        self.current_episode_energy = 0  # 当前episode的累积能耗
        self.current_episode_Qt = 0
        self.current_episode_Eb = 0

    def _on_step(self) -> bool:
        # 检查是否有episode结束
        dones = self.locals.get("dones", [])
        rewards = np.array(
            [self.locals.get("infos", [])[0]["current_cost"]], dtype=np.float32
        )

        # 获取时延和能耗信息
        infos = self.locals.get("infos", [{}])
        if infos and len(infos) > 0:
            delay = infos[0].get("total_delay", 0)
            energy = infos[0].get("total_energy", 0)
            Q_t = infos[0].get("Q_t", 0)
            E_b = infos[0].get("E_b", 0)
        else:
            delay = 0
            energy = 0
            Q_t = 0
            E_b = 0

        # 更新当前episode的累积指标
        for done, reward in zip(dones, rewards):
            self.current_episode_reward += reward
            self.current_episode_delay += delay
            self.current_episode_energy += energy
            self.current_episode_Qt += Q_t
            self.current_episode_Eb += E_b

            if done:
                # episode结束，记录指标
                self.rewards.append(self.current_episode_reward)
                self.delays.append(self.current_episode_delay)
                self.energies.append(self.current_episode_energy)
                self.Qts.append(self.current_episode_Qt)
                self.Ebs.append(self.current_episode_Eb)
                self.current_episode_reward = 0
                self.current_episode_delay = 0
                self.current_episode_energy = 0
                self.current_episode_Qt = 0
                self.current_episode_Eb = 0

                # 保存指标数据
                np.save(
                    os.path.join(self.log_path, "episode_rewards.npy"),
                    np.array(self.rewards),
                )
                np.save(
                    os.path.join(self.log_path, "episode_delays.npy"),
                    np.array(self.delays),
                )
                np.save(
                    os.path.join(self.log_path, "episode_energies.npy"),
                    np.array(self.energies),
                )
                np.save(
                    os.path.join(self.log_path, "episode_queue_length.npy"),
                    np.array(self.Qts),
                )
                np.save(
                    os.path.join(self.log_path, "episode_battery_level.npy"),
                    np.array(self.Ebs),
                )

                # 绘制奖励图表
                plt.figure(figsize=(10, 6))
                plt.plot(range(1, len(self.rewards) + 1), self.rewards)
                plt.xlabel("Episode")
                plt.ylabel("Episode Reward")
                plt.title("Episode Rewards over Time")
                plt.grid(True)
                plt.savefig(os.path.join(self.log_path, "episode_rewards.png"))
                plt.close()

                # 绘制时延图表
                plt.figure(figsize=(10, 6))
                plt.plot(range(1, len(self.delays) + 1), self.delays)
                plt.xlabel("Episode")
                plt.ylabel("Episode Delay")
                plt.title("Episode Delays over Time")
                plt.grid(True)
                plt.savefig(os.path.join(self.log_path, "episode_delays.png"))
                plt.close()

                # 绘制能耗图表
                plt.figure(figsize=(10, 6))
                plt.plot(range(1, len(self.energies) + 1), self.energies)
                plt.xlabel("Episode")
                plt.ylabel("Episode Energy")
                plt.title("Episode Energies over Time")
                plt.grid(True)
                plt.savefig(os.path.join(self.log_path, "episode_energies.png"))
                plt.close()

                # 绘制Qt图表
                plt.figure(figsize=(10, 6))
                plt.plot(range(1, len(self.Qts) + 1), self.Qts)
                plt.xlabel("Episode")
                plt.ylabel("Queue Length")
                plt.title("Task Queue Length over Time")
                plt.grid(True)
                plt.savefig(os.path.join(self.log_path, "episode_queue_length.png"))
                plt.close()

                # 绘制Eb图表
                plt.figure(figsize=(10, 6))
                plt.plot(range(1, len(self.Ebs) + 1), self.Ebs)
                plt.xlabel("Episode")
                plt.ylabel("Battery Level")
                plt.title("Battery Level over Time")
                plt.grid(True)
                plt.savefig(os.path.join(self.log_path, "episode_battery_level.png"))
                plt.close()

                # 绘制综合图表
                plt.figure(figsize=(12, 8))
                plt.subplot(5, 1, 1)
                plt.plot(range(1, len(self.rewards) + 1), self.rewards)
                plt.ylabel("Reward")
                plt.grid(True)

                plt.subplot(5, 1, 2)
                plt.plot(range(1, len(self.delays) + 1), self.delays)
                plt.ylabel("Delay")
                plt.grid(True)

                plt.subplot(5, 1, 3)
                plt.plot(range(1, len(self.energies) + 1), self.energies)
                plt.ylabel("Energy")
                plt.grid(True)

                plt.subplot(5, 1, 4)
                plt.plot(range(1, len(self.Qts) + 1), self.Qts)
                plt.ylabel("Queue Length")
                plt.grid(True)

                plt.subplot(5, 1, 5)
                plt.plot(range(1, len(self.Ebs) + 1), self.Ebs)
                plt.xlabel("Episode")
                plt.ylabel("Battery Level")
                plt.grid(True)

                plt.tight_layout()
                plt.savefig(os.path.join(self.log_path, "episode_metrics.png"))
                plt.close()

        if self.eval_freq > 0 and self.n_calls % self.eval_freq == 0:
            # 执行原有的评估逻辑
            result = super()._on_step()

            # 获取最近一次评估的info
            if len(self.evaluations_timesteps) > 0:
                # 从self.locals中获取评估信息
                eval_infos = self.locals.get("infos", [])
                episode_costs = []
                episode_delays = []
                episode_energies = []

                # 获取评估结果
                for info in eval_infos:
                    if "cost" in info:
                        episode_costs.append(info["cost"])
                    if "total_delay" in info:
                        episode_delays.append(info["total_delay"])
                    if "total_energy" in info:
                        episode_energies.append(info["total_energy"])

                # 计算并记录平均指标
                if episode_costs:
                    mean_cost = np.mean(episode_costs)
                    self.costs.append(mean_cost)
                    self.logger.record("eval/mean_cost", mean_cost)

                if episode_delays:
                    mean_delay = np.mean(episode_delays)
                    self.logger.record("eval/mean_delay", mean_delay)

                if episode_energies:
                    mean_energy = np.mean(episode_energies)
                    self.logger.record("eval/mean_energy", mean_energy)

                self.logger.dump(self.n_calls)  # 确保日志被打印

            return result

        return True


class CustomTD3(TD3):
    def collect_rollouts(
        self,
        env,
        callback,
        train_freq,
        replay_buffer,
        action_noise=None,
        learning_starts=0,
        log_interval=None,
    ):
        """
        重写collect_rollouts方法，在收集经验时检查truncated标志
        """
        num_collected_steps = 0
        num_collected_episodes = 0

        assert train_freq.frequency > 0, "Should at least collect one step or episode."

        if env.num_envs > 1:
            assert train_freq.unit == "step", (
                "Vector env does not support episode-based training"
            )

        # Vectorize action noise if needed
        if (
            action_noise is not None
            and env.num_envs > 1
            and not isinstance(action_noise, VectorizedActionNoise)
        ):
            action_noise = VectorizedActionNoise(action_noise, env.num_envs)

        if self.use_sde:
            self.actor.reset_noise(env.num_envs)

        callback.on_rollout_start()
        continue_training = True

        # 记录尝试的次数，防止无限循环
        attempt_count = 0
        max_attempts = train_freq.frequency * 2

        while (
            should_collect_more_steps(
                train_freq, num_collected_steps, num_collected_episodes
            )
            and attempt_count < max_attempts
        ):
            attempt_count += 1

            if (
                self.use_sde
                and self.sde_sample_freq > 0
                and num_collected_steps % self.sde_sample_freq == 0
            ):
                # Sample a new noise matrix
                self.actor.reset_noise(env.num_envs)

            # Select action randomly or according to policy
            actions, buffer_actions = self._sample_action(
                learning_starts, action_noise, env.num_envs
            )

            # Rescale and perform action
            try:
                new_obs, rewards, dones, infos = env.step(actions)
            except Exception as e:
                print(f"环境step发生错误: {e}")
                # 如果环境抛出错误，重置环境并继续
                self._last_obs = env.reset()
                continue

            self.num_timesteps += env.num_envs
            num_collected_steps += 1

            # Give access to local variables
            callback.update_locals(locals())
            # Only stop training if return value is False, not when it is None.
            if callback.on_step() is False:
                return RolloutReturn(
                    num_collected_steps * env.num_envs,
                    num_collected_episodes,
                    continue_training=False,
                )

            # Retrieve reward and episode length if using Monitor wrapper
            self._update_info_buffer(infos, dones)

            # 如果info中的truncated为True，则跳过存储这个经验
            store_transition = True
            battery_error = False
            for idx, info in enumerate(infos):
                if info.get("truncated", False):
                    store_transition = False
                # 检查是否有特定错误信息
                if "error" in info and "battery" in info["error"].lower():
                    battery_error = True
                    print(f"检测到电池能量不足: {info['error']}")

            if battery_error:
                # 如果是电池能量不足，重置环境
                print("由于电池能量不足，重置环境...")
                self._last_obs = env.reset()
                continue

            # 只有在不截断的情况下才存储经验
            if store_transition:
                # Store data in replay buffer (normalized action and unnormalized observation)
                self._store_transition(
                    replay_buffer, buffer_actions, new_obs, rewards, dones, infos
                )

            self._update_current_progress_remaining(
                self.num_timesteps, self._total_timesteps
            )

            # For DQN, check if the target network should be updated
            # and update the exploration schedule
            # For SAC/TD3, the update is dones as the same time as the gradient update
            # see https://github.com/hill-a/stable-baselines/issues/900
            self._on_step()

            for idx, done in enumerate(dones):
                if done:
                    # Update stats
                    num_collected_episodes += 1
                    self._episode_num += 1

                    if action_noise is not None:
                        kwargs = dict(indices=[idx]) if env.num_envs > 1 else {}
                        action_noise.reset(**kwargs)

                    # Log training infos
                    if (
                        log_interval is not None
                        and self._episode_num % log_interval == 0
                    ):
                        self._dump_logs()

        # 如果尝试次数过多但收集的步骤很少，打印警告
        if (
            attempt_count >= max_attempts
            and num_collected_steps < train_freq.frequency * 0.5
        ):
            print(
                f"警告: 尝试了{attempt_count}次，但只收集到{num_collected_steps}步有效数据。"
            )

        callback.on_rollout_end()

        return RolloutReturn(
            num_collected_steps * env.num_envs,
            num_collected_episodes,
            continue_training,
        )


if __name__ == "__main__":
    # 创建环境
    n_envs = 1
    env = DummyVecEnv([make_env(i) for i in range(n_envs)])
    env = VecNormalize(env, norm_obs=True, norm_reward=True)

    # 创建评估环境
    eval_env = DummyVecEnv([make_env(i) for i in range(1)])
    eval_env = VecNormalize(eval_env, norm_obs=True, norm_reward=True)
    eval_env.obs_rms = env.obs_rms
    eval_env.ret_rms = env.ret_rms

    # 创建模型
    policy_kwargs = dict(
        features_extractor_class=UAVEnvExtrator,
        features_extractor_kwargs=dict(features_dim=128),
        net_arch=dict(pi=[64, 64], qf=[64, 64]),
    )

    # 创建噪声对象用于探索
    n_actions = env.action_space.shape[-1]
    action_noise = NormalActionNoise(
        mean=np.zeros(n_actions), sigma=0.3 * np.ones(n_actions)
    )

    # 创建自定义TD3模型
    model = CustomTD3(
        "MlpPolicy",
        env,
        learning_rate=params["learning_rate"],
        buffer_size=params["buffer_size"],
        learning_starts=params["learning_starts"],
        batch_size=params["batch_size"],
        tau=params["tau"],
        gamma=0.99,
        train_freq=1,
        gradient_steps=1,
        action_noise=action_noise,
        policy_delay=params["policy_delay"],  # TD3特有参数
        target_policy_noise=params["target_policy_noise"],  # TD3特有参数
        target_noise_clip=params["target_noise_clip"],  # TD3特有参数
        policy_kwargs=policy_kwargs,
        verbose=1,
    )

    # 创建日志和模型文件夹
    if not os.path.exists("./logs/td3/v={}M={}N={}/".format(args.v, args.M, args.N)):
        os.makedirs("./logs/td3/v={}M={}N={}/".format(args.v, args.M, args.N))
    if not os.path.exists(
        "./logs/td3/v={}M={}N={}/evaluations/".format(args.v, args.M, args.N)
    ):
        os.makedirs(
            "./logs/td3/v={}M={}N={}/evaluations/".format(args.v, args.M, args.N)
        )
    if not os.path.exists(
        "./best_model/td3/v={}M={}N={}/".format(args.v, args.M, args.N)
    ):
        os.makedirs("./best_model/td3/v={}M={}N={}/".format(args.v, args.M, args.N))

    # 创建回调函数
    checkpoint_callback = CheckpointCallback(
        save_freq=10000,
        save_path="./logs/td3/v={}M={}N={}/".format(args.v, args.M, args.N),
        name_prefix="td3_model",
    )

    # 创建评估回调
    eval_callback = CustomEvalCallback(
        eval_env,
        best_model_save_path="./best_model/td3/v={}M={}N={}/".format(
            args.v, args.M, args.N
        ),
        log_path="./logs/td3/v={}M={}N={}/".format(args.v, args.M, args.N),
        eval_freq=10000,
        deterministic=True,
        render=False,
    )

    # 训练模型
    model.learn(
        total_timesteps=50000,
        callback=[checkpoint_callback, eval_callback],
        log_interval=10000,
    )

    # 报告训练过程中的最终cost作为优化目标
    if args.nni:
        nni.report_final_result(np.mean(eval_callback.costs))
