import gym
import torch
from torch import nn
import numpy as np

from stable_baselines3.common.torch_layers import BaseFeaturesExtractor

class UAVEnvExtrator(BaseFeaturesExtractor):
    def __init__(self, observation_space: gym.spaces.Box, features_dim: int = 256):
        super().__init__(observation_space, features_dim)
        
        # 计算输入维度
        n_input = int(np.prod(observation_space.shape))
        
        # 定义网络结构 - 增加网络容量以处理多无人机环境
        self.network = nn.Sequential(
            nn.Linear(n_input, 256),
            nn.ReLU(),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, features_dim),
            nn.ReLU()
        )
        
    def forward(self, observations: torch.Tensor) -> torch.Tensor:
        # 直接处理一维状态空间
        return self.network(observations)