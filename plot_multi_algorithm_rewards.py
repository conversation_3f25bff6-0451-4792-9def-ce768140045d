#!/usr/bin/env python3
"""
Simple script for quick multi-algorithm rewards comparison.
This is a simplified version for basic usage.
"""

import os

import matplotlib.pyplot as plt
import numpy as np


def plot_multi_algorithm_rewards():
    """
    Plot reward changes over episodes for multiple algorithms in the same graph.
    Data is loaded from logs/{algorithm}/v=0.0M=1N=2/evaluations/episode_rewards.npy
    """

    # Define algorithms to compare
    algorithms = ["a2c", "ppo", "ddpg", "td3"]

    # Configuration parameters
    config = "v=0.0M=1N=2"

    # Create figure and axis
    plt.figure(figsize=(12, 8))

    # Colors for different algorithms
    colors = ["blue", "red", "green", "orange"]

    # Track if any data was loaded
    data_loaded = False

    for i, algorithm in enumerate(algorithms):
        # Construct path to episode rewards data
        data_path = f"logs/{algorithm}/{config}/evaluations/episode_rewards.npy"

        if os.path.exists(data_path):
            try:
                # Load the rewards data
                rewards = np.load(data_path)

                # Create episode numbers (x-axis)
                episodes = np.arange(1, len(rewards) + 1)

                # Plot the rewards
                plt.plot(
                    episodes,
                    rewards,
                    color=colors[i % len(colors)],
                    label=algorithm.upper(),
                    linewidth=2,
                    alpha=0.8,
                )

                data_loaded = True
                print(f"Loaded data for {algorithm}: {len(rewards)} episodes")

            except Exception as e:
                print(f"Error loading data for {algorithm}: {e}")
        else:
            print(f"Data file not found for {algorithm}: {data_path}")

    if not data_loaded:
        print("No data was loaded. Please check the file paths.")
        return

    # Customize the plot
    plt.xlabel("Episode", fontsize=14)
    plt.ylabel("Reward", fontsize=14)
    plt.title(
        f"Episode Rewards Comparison Across Algorithms\n(Configuration: {config})",
        fontsize=16,
    )
    plt.legend(fontsize=12)
    plt.grid(True, alpha=0.3)

    # Improve layout
    plt.tight_layout()

    # Create output directory
    output_dir = "results/algorithm_comparison"
    os.makedirs(output_dir, exist_ok=True)

    # Save the plot
    output_path = f"{output_dir}/multi_algorithm_rewards_comparison_{config.replace('=', '_').replace('.', '_')}.png"
    plt.savefig(output_path, dpi=300, bbox_inches="tight")
    print(f"Plot saved as: {output_path}")


if __name__ == "__main__":
    plot_multi_algorithm_rewards()
