# 灵活绘图工具使用指南

## 概述

`flexible_reward_plotter.py` 现在支持三种绘图模式和多种指标，可以灵活地比较不同算法和配置的性能。

## 可用指标

- `episode_rewards` - 奖励
- `episode_delays` - 延迟
- `episode_energies` - 能源消耗
- `episode_battery_level` - 电池电量
- `episode_queue_length` - 队列长度

## 三种绘图模式

### 1. 算法模式 (algorithms)
比较多个算法在相同配置下的表现

```bash
# 比较所有算法的奖励（默认）
python flexible_reward_plotter.py

# 比较特定算法的延迟
python flexible_reward_plotter.py --mode algorithms --metric episode_delays --algorithms a2c ppo --config "v=0.0M=1N=2"

# 比较电池电量
python flexible_reward_plotter.py --mode algorithms --metric episode_battery_level --config "v=1.0M=1N=2"
```

### 2. V值比较模式 (v-comparison)
比较单个算法在不同v值下的表现

```bash
# 比较A2C在不同v值下的奖励
python flexible_reward_plotter.py --mode v-comparison --algorithm a2c --base-config "M=1N=2"

# 比较PPO在不同v值下的能源消耗
python flexible_reward_plotter.py --mode v-comparison --algorithm ppo --metric episode_energies --v-values 0.0 1.0
```

### 3. 灵活模式 (flexible) ⭐ 新功能
任意组合算法和配置

```bash
# 你要求的功能：A2C的v=0.0和v=1.0，以及其他算法的v=1.0
python flexible_reward_plotter.py --mode flexible --configs \
  a2c:v=0.0M=1N=2:"A2C v=0.0" \
  a2c:v=1.0M=1N=2:"A2C v=1.0" \
  ppo:v=1.0M=1N=2:"PPO v=1.0" \
  ddpg:v=1.0M=1N=2:"DDPG v=1.0" \
  td3:v=1.0M=1N=2:"TD3 v=1.0"

# 比较延迟指标
python flexible_reward_plotter.py --mode flexible --metric episode_delays --configs \
  a2c:v=0.0M=1N=2 \
  a2c:v=1.0M=1N=2 \
  ppo:v=1.0M=1N=2

# 带平滑的能源消耗比较
python flexible_reward_plotter.py --mode flexible --metric episode_energies --smooth 10 --configs \
  a2c:v=0.0M=1N=2:"A2C Conservative" \
  a2c:v=1.0M=1N=2:"A2C Aggressive"
```

## 配置格式

灵活模式中的 `--configs` 参数支持以下格式：

1. **基本格式**: `algorithm:config`
   - 例如: `a2c:v=0.0M=1N=2`

2. **带标签格式**: `algorithm:config:label`
   - 例如: `a2c:v=0.0M=1N=2:"A2C Conservative"`

## 常用选项

- `--smooth N`: 应用N点移动平均平滑
- `--no-stats`: 不显示统计信息
- `--list`: 列出所有可用的算法、配置和指标

## 实际使用示例

### 示例1: 参数分析
```bash
# 分析v参数对A2C性能的影响
python flexible_reward_plotter.py --mode flexible --configs \
  a2c:v=0.0M=1N=2:"v=0.0" \
  a2c:v=1.0M=1N=2:"v=1.0" \
  --metric episode_rewards
```

### 示例2: 算法对比
```bash
# 在相同配置下比较不同算法
python flexible_reward_plotter.py --mode flexible --configs \
  a2c:v=1.0M=1N=2:"A2C" \
  ppo:v=1.0M=1N=2:"PPO" \
  ddpg:v=1.0M=1N=2:"DDPG" \
  td3:v=1.0M=1N=2:"TD3" \
  --metric episode_delays
```

### 示例3: 多指标分析
```bash
# 分析能源消耗
python flexible_reward_plotter.py --mode flexible --metric episode_energies --configs \
  a2c:v=0.0M=1N=2 a2c:v=1.0M=1N=2 --smooth 5

# 分析实验
python flexible_reward_plotter.py --mode flexible --metric episode_delays --configs \
  a2c:v=0.0M=1N=4 a2c:v=1.0M=1N=4 ddpg:v=1.0M=1N=4 ppo:v=1.0M=1N=4 td3:v=1.0M=1N=4 --smooth 5

# 分析电池电量
python flexible_reward_plotter.py --mode flexible --metric episode_battery_level --configs \
  a2c:v=0.0M=1N=2 a2c:v=1.0M=1N=2
```

## 输出文件

生成的图片文件会根据模式和内容自动命名：
- 灵活模式: `flexible_{metric}_comparison_{config_summary}.png`
- 带平滑: 文件名末尾添加 `_smooth{N}`

## 编程接口

也可以直接在Python代码中使用：

```python
from flexible_reward_plotter import plot_flexible_comparison

# 你要求的功能
algorithm_configs = [
    {"algorithm": "a2c", "config": "v=0.0M=1N=2", "label": "A2C v=0.0"},
    {"algorithm": "a2c", "config": "v=1.0M=1N=2", "label": "A2C v=1.0"},
    {"algorithm": "ppo", "config": "v=1.0M=1N=2", "label": "PPO v=1.0"},
    {"algorithm": "ddpg", "config": "v=1.0M=1N=2", "label": "DDPG v=1.0"}
]

plot_flexible_comparison(
    algorithm_configs=algorithm_configs,
    metric="episode_rewards",
    show_stats=True
)
```
