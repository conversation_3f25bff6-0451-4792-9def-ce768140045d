# 无人机任务卸载 - 强化学习方法比较

本项目实现了无人机辅助移动边缘计算系统中的任务卸载策略，比较了多种强化学习算法的性能表现。

## 项目概述

在无人机辅助移动边缘计算场景中，我们需要决定将计算任务在终端设备本地处理还是卸载到无人机上进行计算。这一决策直接影响任务处理的时延、能耗以及系统的整体性能。本项目实现并比较了以下几种任务卸载策略：

1. **完全本地计算**：所有任务都在终端设备上本地计算 (R_n = 0) (TODO)
2. **完全卸载**：所有任务都卸载到无人机上计算 (R_n = 1) (TODO)
3. **基于SAC的动态卸载**：使用Soft Actor-Critic算法学习最优卸载决策
4. **基于DDPG的动态卸载**：使用Deep Deterministic Policy Gradient算法学习最优卸载决策
5. **基于A2C的动态卸载**：使用Advantage Actor-Critic算法学习最优卸载决策
6. **基于李雅普诺夫优化的动态卸载**：考虑队列状态和电池状态的全局最优决策

## 关键性能指标

本项目关注以下性能指标：

1. **任务时延**：包括本地计算时延、传输时延和UAV计算时延
2. **能量消耗**：包括本地计算能耗、传输能耗和UAV计算能耗
3. **任务队列长度(Qt)**：当前系统中等待处理的任务数量
4. **电池能量水平(Eb)**：UAV当前剩余的电池能量

## 项目结构

```
├── envs/                          # 环境定义
│   └── single_fixed_envs.py       # UAV环境实现
├── logs/                          # 训练日志和结果
│   ├── fixed_eta/                 # SAC算法结果
│   ├── ddpg/                      # DDPG算法结果
│   └── a2c/                       # A2C算法结果
├── policy.py                      # 自定义策略实现
├── trainWithFixedEta.py           # SAC算法训练实现
├── train_ddpg.py                  # DDPG算法训练实现
├── train_a2c.py                   # A2C算法训练实现
├── train_lyapunov.py              # 李雅普诺夫优化实现
├── train_local_compute.py         # 完全本地计算策略
├── train_edge_compute.py          # 完全边缘计算策略
├── compare_offload_strategies.py  # 卸载策略比较
├── compare_algorithms.py          # 强化学习算法性能比较
├── test_baseline.py               # 基准测试
├── inference.py                   # 模型推理
└── environment.yml                # 环境依赖配置
```

## 算法实现

### 1. Soft Actor-Critic (SAC)
SAC是一种基于最大熵强化学习的off-policy算法，它通过最大化预期回报和策略熵的加权和，鼓励探索以找到鲁棒的策略。

文件：`trainWithFixedEta.py`

### 2. Deep Deterministic Policy Gradient (DDPG)
DDPG是一种off-policy算法，它结合了DQN和确定性策略梯度，适用于连续动作空间问题。它使用Actor网络输出确定性动作，Critic网络评估动作价值。

文件：`train_ddpg.py`

### 3. Advantage Actor-Critic (A2C)
A2C是一种on-policy算法，它使用Actor网络学习策略，Critic网络估计值函数。通过减少方差来提高稳定性，并使用多步回报更新值函数。

文件：`train_a2c.py`

## 使用方法

### 环境配置
```bash
# 创建conda环境
conda env create -f environment.yml
conda activate uav-task-offloading
```

### 训练模型
```bash
# 训练SAC模型
python trainWithFixedEta.py

# 训练DDPG模型
python train_ddpg.py

# 训练A2C模型
python train_a2c.py
```

### 比较算法性能
```bash
# 比较三种算法性能
python compare_algorithms.py
```

### 比较卸载策略
```bash
# 比较各种卸载策略
python compare_offload_strategies.py
```

## 结果分析

运行`compare_algorithms.py`后，系统将生成以下可视化结果：

1. **任务时延分布对比**：直观比较三种算法下任务处理时延的分布情况
2. **能耗分布对比**：比较三种算法的能量消耗分布情况
3. **无人机剩余电量对比**：箱线图展示三种算法下无人机电池水平分布
4. **任务队列长度对比**：箱线图展示三种算法维持的任务队列长度分布
5. **训练进度曲线**：展示三种算法在训练过程中各指标的变化趋势
6. **算法性能雷达图**：从多个维度综合展示三种算法的性能表现