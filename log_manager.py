import os
from typing import Optional


def generate_experiment_suffix(args, exclude_params=None):
    """
    根据参数自动生成实验后缀
    
    Args:
        args: 包含实验参数的对象
        exclude_params: 要排除的参数列表，默认排除常见的非实验参数
    
    Returns:
        str: 实验后缀，如 "v=1.0M=2N=3lr=0.001"
    """
    if exclude_params is None:
        exclude_params = {
            'nni', 'log_base_path', 'model_base_path', 'help', 
            'algorithm', 'verbose', 'device', 'seed'
        }
    
    # 获取所有参数
    if hasattr(args, '__dict__'):
        params = args.__dict__
    else:
        params = vars(args)
    
    # 过滤并排序参数
    filtered_params = {}
    for key, value in params.items():
        if key not in exclude_params and value is not None:
            filtered_params[key] = value
    
    # 按参数名排序，确保路径一致性
    sorted_params = sorted(filtered_params.items())
    
    # 生成后缀
    suffix_parts = []
    for key, value in sorted_params:
        # 格式化数值
        if isinstance(value, float):
            if value == int(value):
                suffix_parts.append(f"{key}={int(value)}")
            else:
                suffix_parts.append(f"{key}={value}")
        else:
            suffix_parts.append(f"{key}={value}")
    
    return "".join(suffix_parts)


def get_paths(algorithm: str, args, base_path: Optional[str] = None, model_base_path: Optional[str] = None):
    """
    根据算法和参数生成所有路径
    
    Args:
        algorithm: 算法名称
        args: 参数对象
        base_path: 自定义日志基础路径
        model_base_path: 自定义模型基础路径
    
    Returns:
        dict: 包含所有路径的字典
    """
    # 使用传入的路径或从args获取或使用默认值
    log_base = base_path or getattr(args, 'log_base_path', None) or "./logs"
    model_base = model_base_path or getattr(args, 'model_base_path', None) or "./best_model"
    
    # 生成实验后缀
    experiment_suffix = generate_experiment_suffix(args)
    
    # 构建路径
    log_path = os.path.join(log_base, algorithm, experiment_suffix)
    eval_log_path = os.path.join(log_path, "evaluations")
    model_path = os.path.join(model_base, algorithm, experiment_suffix)
    
    # 创建目录
    for path in [log_path, eval_log_path, model_path]:
        if not os.path.exists(path):
            os.makedirs(path, exist_ok=True)
            print(f"创建目录: {path}")
    
    return {
        'log_path': log_path,
        'eval_log_path': eval_log_path,
        'model_path': model_path,
        'checkpoint_path': log_path,  # 检查点保存在主日志目录
        'tensorboard_path': os.path.join(log_base, algorithm)
    }


def add_log_arguments(parser):
    """
    向ArgumentParser添加日志相关的命令行参数
    
    Args:
        parser: argparse.ArgumentParser实例
    """
    parser.add_argument(
        "--log_base_path", 
        type=str, 
        default=None,
        help="自定义日志基础路径，默认为 './logs'"
    )
    parser.add_argument(
        "--model_base_path", 
        type=str, 
        default=None,
        help="自定义模型保存基础路径，默认为 './best_model'"
    )


# 为了向后兼容，保留原有的函数名
def create_log_manager_from_args(args, algorithm: str):
    """向后兼容的函数，返回路径字典"""
    return get_paths(algorithm, args)


class LogManager:
    """简化的日志管理器，主要用于向后兼容"""
    
    def __init__(self, algorithm: str, args=None, **kwargs):
        if args is None:
            # 如果没有args，从kwargs创建一个简单的对象
            class SimpleArgs:
                pass
            args = SimpleArgs()
            for key, value in kwargs.items():
                setattr(args, key, value)
        
        self.paths = get_paths(algorithm, args)
        self.algorithm = algorithm
        self.args = args
    
    def get_log_path(self) -> str:
        return self.paths['log_path']
    
    def get_eval_log_path(self) -> str:
        return self.paths['eval_log_path']
    
    def get_model_path(self) -> str:
        return self.paths['model_path']
    
    def get_checkpoint_path(self) -> str:
        return self.paths['checkpoint_path']
    
    def get_tensorboard_log_dir(self) -> str:
        return self.paths['tensorboard_path']
