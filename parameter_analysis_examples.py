#!/usr/bin/env python3
"""
Parameter Analysis Examples - 参数分析示例

展示如何使用新的参数分析工具进行各种分析。
"""

import subprocess
import sys
import time

def run_analysis(command, description):
    """运行分析命令并显示描述"""
    print(f"\n{'='*60}")
    print(f"🔍 {description}")
    print(f"{'='*60}")
    print(f"Command: {command}")
    print("-" * 60)
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=60)
        if result.returncode == 0:
            print("✅ Analysis completed successfully!")
            print(result.stdout)
        else:
            print("❌ Analysis failed!")
            print(result.stderr)
    except subprocess.TimeoutExpired:
        print("⏰ Analysis timed out!")
    except Exception as e:
        print(f"💥 Error: {e}")
    
    time.sleep(1)  # 短暂暂停

def main():
    print("🚀 Parameter Analysis Examples")
    print("=" * 60)
    
    # 示例1: 设备扩展性分析 - 延迟
    run_analysis(
        'python parameter_analysis_plotter.py --analysis device '
        '--conditions a2c:1.0:4:"A2C v=1.0" a2c:0.0:4:"A2C v=0.0" ppo:1.0:4:"PPO v=1.0" '
        '--metric episode_delays',
        "设备扩展性分析 - 延迟指标 (固定N=4)"
    )
    
    # 示例2: 设备扩展性分析 - 能耗
    run_analysis(
        'python parameter_analysis_plotter.py --analysis device '
        '--conditions a2c:1.0:8:"A2C v=1.0" a2c:0.0:8:"A2C v=0.0" '
        '--metric episode_energies',
        "设备扩展性分析 - 能耗指标 (固定N=8)"
    )
    
    # 示例3: 任务负载分析 - 延迟
    run_analysis(
        'python parameter_analysis_plotter.py --analysis workload '
        '--conditions a2c:1.0:1:"A2C v=1.0" a2c:0.0:1:"A2C v=0.0" ppo:1.0:1:"PPO v=1.0" '
        '--metric episode_delays',
        "任务负载分析 - 延迟指标 (固定M=1)"
    )
    
    # 示例4: 任务负载分析 - 能耗
    run_analysis(
        'python parameter_analysis_plotter.py --analysis workload '
        '--conditions a2c:1.0:2:"A2C v=1.0" a2c:0.0:2:"A2C v=0.0" '
        '--metric episode_energies',
        "任务负载分析 - 能耗指标 (固定M=2)"
    )
    
    # 示例5: 多算法设备扩展性对比
    run_analysis(
        'python parameter_analysis_plotter.py --analysis device '
        '--conditions a2c:1.0:4:"A2C" ppo:1.0:4:"PPO" ddpg:1.0:4:"DDPG" td3:1.0:4:"TD3" '
        '--metric episode_delays',
        "多算法设备扩展性对比 (v=1.0, N=4)"
    )
    
    # 示例6: 电池电量分析
    run_analysis(
        'python parameter_analysis_plotter.py --analysis device '
        '--conditions a2c:1.0:8:"A2C v=1.0" a2c:0.0:8:"A2C v=0.0" '
        '--metric episode_battery_level',
        "设备数量对电池电量的影响"
    )
    
    # 示例7: 队列长度分析
    run_analysis(
        'python parameter_analysis_plotter.py --analysis workload '
        '--conditions a2c:1.0:1:"A2C v=1.0" ppo:1.0:1:"PPO v=1.0" '
        '--metric episode_queue_length',
        "任务负载对队列长度的影响"
    )
    
    print(f"\n{'='*60}")
    print("🎉 所有参数分析示例已完成!")
    print("📊 生成的图片文件:")
    print("   - device_analysis_delays_*.png")
    print("   - device_analysis_energies_*.png") 
    print("   - workload_analysis_delays_*.png")
    print("   - workload_analysis_energies_*.png")
    print("   - device_analysis_battery_*.png")
    print("   - workload_analysis_queue_*.png")
    print(f"{'='*60}")

def show_usage_examples():
    """显示使用示例"""
    print("\n📖 使用示例:")
    print("-" * 40)
    
    examples = [
        {
            "title": "设备扩展性分析",
            "command": "python parameter_analysis_plotter.py --analysis device --conditions a2c:1.0:4 a2c:0.0:4 --metric episode_delays"
        },
        {
            "title": "任务负载分析", 
            "command": "python parameter_analysis_plotter.py --analysis workload --conditions a2c:1.0:1 ppo:1.0:1 --metric episode_delays"
        },
        {
            "title": "能耗效率分析",
            "command": "python parameter_analysis_plotter.py --analysis device --conditions a2c:1.0:8 a2c:0.0:8 --metric episode_energies"
        },
        {
            "title": "多算法对比",
            "command": "python parameter_analysis_plotter.py --analysis device --conditions a2c:1.0:4 ppo:1.0:4 ddpg:1.0:4 --metric episode_delays"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['title']}:")
        print(f"   {example['command']}")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--examples":
        show_usage_examples()
    else:
        main()
