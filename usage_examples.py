#!/usr/bin/env python3
"""
Usage examples for the enhanced flexible_reward_plotter.py
"""

from flexible_reward_plotter import plot_flexible_comparison

def example_1_mixed_algorithms_and_configs():
    """
    Example 1: Plot A2C v=0.0, A2C v=1.0, and other algorithms with v=1.0
    """
    print("Example 1: Mixed algorithms and configurations")
    
    algorithm_configs = [
        {"algorithm": "a2c", "config": "v=0.0M=1N=2", "label": "A2C v=0.0"},
        {"algorithm": "a2c", "config": "v=1.0M=1N=2", "label": "A2C v=1.0"},
        {"algorithm": "ppo", "config": "v=1.0M=1N=2", "label": "PPO v=1.0"},
        {"algorithm": "ddpg", "config": "v=1.0M=1N=2", "label": "DDPG v=1.0"},
        {"algorithm": "td3", "config": "v=1.0M=1N=2", "label": "TD3 v=1.0"}
    ]
    
    plot_flexible_comparison(
        algorithm_configs=algorithm_configs,
        metric="episode_rewards",
        show_stats=True
    )

def example_2_delay_comparison():
    """
    Example 2: Compare delays across different configurations
    """
    print("Example 2: Delay comparison")
    
    algorithm_configs = [
        ("a2c", "v=0.0M=1N=2"),
        ("a2c", "v=1.0M=1N=2"),
        ("ppo", "v=0.0M=1N=2"),
        ("ppo", "v=1.0M=1N=2")
    ]
    
    plot_flexible_comparison(
        algorithm_configs=algorithm_configs,
        metric="episode_delays",
        smooth_window=10,
        show_stats=True
    )

def example_3_energy_consumption():
    """
    Example 3: Compare energy consumption with custom labels
    """
    print("Example 3: Energy consumption comparison")
    
    algorithm_configs = [
        ("a2c", "v=0.0M=1N=2"),
        ("a2c", "v=1.0M=1N=2")
    ]
    
    custom_labels = ["A2C Conservative", "A2C Aggressive"]
    
    plot_flexible_comparison(
        algorithm_configs=algorithm_configs,
        metric="episode_energies",
        custom_labels=custom_labels,
        smooth_window=5,
        show_stats=True
    )

def example_4_battery_level():
    """
    Example 4: Battery level comparison
    """
    print("Example 4: Battery level comparison")
    
    algorithm_configs = [
        {"algorithm": "a2c", "config": "v=0.0M=1N=4", "label": "A2C v=0.0 (M=1N=4)"},
        {"algorithm": "a2c", "config": "v=1.0M=1N=4", "label": "A2C v=1.0 (M=1N=4)"},
        {"algorithm": "ppo", "config": "v=0.0M=1N=4", "label": "PPO v=0.0 (M=1N=4)"},
        {"algorithm": "ppo", "config": "v=1.0M=1N=4", "label": "PPO v=1.0 (M=1N=4)"}
    ]
    
    plot_flexible_comparison(
        algorithm_configs=algorithm_configs,
        metric="episode_battery_level",
        show_stats=True
    )

if __name__ == "__main__":
    print("Running flexible plotting examples...")
    print("=" * 50)
    
    # Run examples
    example_1_mixed_algorithms_and_configs()
    example_2_delay_comparison()
    example_3_energy_consumption()
    example_4_battery_level()
    
    print("\nAll examples completed!")
