from stable_baselines3 import SAC
from stable_baselines3.common.vec_env import DummyVecEnv, VecNormalize
from envs.single_fixed_envs import UAVEnv
from policy import UAVEnvExtrator
import numpy as np
import matplotlib.pyplot as plt
from stable_baselines3.common.monitor import Monitor
import os

def make_env():
    def _init():
        env = UAVEnv()
        env = Monitor(env)
        return env
    return _init

def plot_trajectory(env, positions, ue_positions, block_states, episode_num):
    """绘制UAV轨迹和终端位置"""
    plt.figure(figsize=(10, 10))
    
    # 绘制终端位置
    terminal_positions = ue_positions[-1]  # 使用最后一步的终端位置
    block_status = block_states[-1]  # 使用最后一步的遮挡状态
    
    # 将不同遮挡状态的终端用不同颜色表示
    for i in range(len(terminal_positions)):
        if block_status[i] == 1:
            plt.scatter(terminal_positions[i, 0], terminal_positions[i, 1], 
                       c='r', marker='o', s=80, label='遮挡终端' if i == 0 else "")
        else:
            plt.scatter(terminal_positions[i, 0], terminal_positions[i, 1], 
                       c='g', marker='o', s=80, label='正常终端' if i == 0 else "")
    
    # 绘制UAV轨迹
    uav_positions = np.array(positions)
    plt.plot(uav_positions[:, 0], uav_positions[:, 1], 
             'b-', label='UAV轨迹')
    
    # 绘制起点和终点
    plt.scatter(uav_positions[0, 0], uav_positions[0, 1], 
               c='g', marker='*', s=200, label='起点')
    plt.scatter(uav_positions[-1, 0], uav_positions[-1, 1], 
               c='r', marker='*', s=200, label='终点')
    
    plt.grid(True)
    plt.xlabel('X坐标', fontsize=12)
    plt.ylabel('Y坐标', fontsize=12)
    plt.title('UAV轨迹和终端分布', fontsize=14)
    plt.legend(fontsize=10)
    plt.savefig(f'trajectory_{episode_num}.png')
    plt.close()

def evaluate_episode(model, env, episode_num):
    """评估一个完整的episode"""
    obs = env.reset()
    done = False
    total_reward = 0
    uav_positions = []
    ue_positions = []
    block_states = []
    step_rewards = []
    selected_terminals = []
    costs = []
    total_steps = 0
    
    # 获取原始环境
    venv = env
    while isinstance(venv, VecNormalize):
        venv = venv.venv
    raw_env = venv.envs[0]
    
    # 记录初始状态的UAV位置和终端信息
    N = raw_env.uav_config.N  # 终端数量
    
    # 记录初始状态
    # UAV位置: 索引1-2
    # 终端位置: 索引4到4+2*N-1
    # 终端数据量: 索引4+2*N到4+3*N-1
    # 终端遮挡状态: 索引4+3*N到4+4*N-1
    uav_positions.append(raw_env.state[1:3].copy())
    ue_positions.append(raw_env.state[4:4+2*N].reshape(-1, 2).copy())
    block_states.append(raw_env.state[4+3*N:4+4*N].copy())
    
    while not done:
        action, _ = model.predict(obs, deterministic=True)
        obs, reward, done, info = env.step(action)
        
        # 记录UAV信息 - UAV位置在索引1-2
        uav_positions.append(raw_env.state[1:3].copy())
        
        # 记录终端位置 - 终端位置在索引4到4+2*N-1
        ue_positions.append(raw_env.state[4:4+2*N].reshape(-1, 2).copy())
        
        # 记录终端遮挡状态 - 遮挡状态在索引4+3*N到4+4*N-1
        block_states.append(raw_env.state[4+3*N:4+4*N].copy())
        
        # 记录选择的终端
        selected_terminals.append(int(action[0][0]))
        
        # 记录奖励和代价
        total_reward += reward[0]
        step_rewards.append(reward[0])
        
        if "cost" in info[0]:
            costs.append(info[0]["cost"])
        
        total_steps += 1
        
        if done[0]:
            break
        
    return {
        'total_reward': total_reward,
        'uav_positions': uav_positions,
        'ue_positions': ue_positions,
        'block_states': block_states,
        'step_rewards': step_rewards,
        'selected_terminals': selected_terminals,
        'costs': costs,
        'total_steps': total_steps
    }

def main():
    # 创建推理环境
    env = DummyVecEnv([make_env()])
    
    try:
        # 加载归一化环境状态
        env = VecNormalize.load("vec_normalize.pkl", env)
        # 设置为只归一化观测，不重新归一化奖励（推理时）
        env.norm_reward = False
        print("成功加载环境标准化参数")
    except Exception as e:
        print(f"加载环境标准化参数失败: {e}")
        print("创建新的标准化环境")
        env = VecNormalize(env, norm_obs=True, norm_reward=False)
    
    # 加载模型策略
    policy_kwargs = dict(
        features_extractor_class=UAVEnvExtrator,
        features_extractor_kwargs=dict(features_dim=128),
        net_arch=dict(
            pi=[64, 64],
            qf=[64, 64]
        )
    )
    
    # 优先加载最优模型，如果不存在则加载最后一个检查点
    model_paths = [
        "./best_model/best_model.zip",
        "final_model.zip"
    ]
    
    model_path = None
    for path in model_paths:
        if os.path.exists(path):
            model_path = path
            break
    
    # 如果上述路径都不存在，查找检查点
    if model_path is None:
        try:
            checkpoints = [f for f in os.listdir("./logs/") if f.startswith("sac_model_")]
            if checkpoints:
                latest_checkpoint = max(checkpoints, key=lambda x: int(x.split("_")[-1]))
                model_path = f"./logs/{latest_checkpoint}"
        except Exception as e:
            print(f"查找检查点失败: {e}")
            print("未找到可用模型，请先训练模型")
            return
    
    if model_path is None:
        print("未找到可用模型，请先训练模型")
        return
    
    print(f"加载模型: {model_path}")
    try:
        model = SAC.load(model_path, env=env)
        print("模型加载成功")
    except Exception as e:
        print(f"模型加载失败: {e}")
        return
    
    # 评估多个episode
    n_episodes = 5
    all_results = []
    
    for ep in range(n_episodes):
        print(f"\n开始评估 Episode {ep + 1}...")
        results = evaluate_episode(model, env, ep)
        all_results.append(results)
        
        print(f"Episode {ep + 1} 结果:")
        print(f"总奖励: {results['total_reward']:.2f}")
        print(f"总步数: {results['total_steps']}")
        if results['costs']:
            print(f"平均代价: {np.mean(results['costs']):.4f}")
        print(f"选择终端频率: {np.bincount(results['selected_terminals'])}")
        
        # 绘制轨迹图
        plot_trajectory(env, results['uav_positions'], 
                       results['ue_positions'], 
                       results['block_states'], ep)
        print(f"轨迹图已保存为 trajectory_{ep}.png")
        
    # 计算统计信息
    rewards = [r['total_reward'] for r in all_results]
    if all(len(r['costs']) > 0 for r in all_results):
        costs = [np.mean(r['costs']) for r in all_results]
        print(f"平均代价: {np.mean(costs):.4f} ± {np.std(costs):.4f}")
    
    print("\n统计信息:")
    print(f"平均奖励: {np.mean(rewards):.2f} ± {np.std(rewards):.2f}")
    
    # 绘制平均奖励随时间变化图
    plt.figure(figsize=(10, 6))
    steps_per_episode = [len(r['step_rewards']) for r in all_results]
    max_steps = max(steps_per_episode)
    
    # 填充缺失值
    padded_rewards = []
    for rewards in [r['step_rewards'] for r in all_results]:
        padded = rewards + [np.nan] * (max_steps - len(rewards))
        padded_rewards.append(padded)
    
    # 计算平均值和标准差
    mean_rewards = np.nanmean(padded_rewards, axis=0)
    std_rewards = np.nanstd(padded_rewards, axis=0)
    
    # 绘图
    plt.plot(mean_rewards, 'b-', label='平均奖励')
    plt.fill_between(range(len(mean_rewards)), 
                    mean_rewards - std_rewards, 
                    mean_rewards + std_rewards, 
                    alpha=0.2, color='b')
    plt.grid(True)
    plt.xlabel('步数', fontsize=12)
    plt.ylabel('奖励值', fontsize=12) 
    plt.title('推理过程中的平均奖励变化', fontsize=14)
    plt.legend(fontsize=10)
    plt.savefig('inference_rewards.png')
    plt.close()
    print("奖励变化图已保存为 inference_rewards.png")

if __name__ == "__main__":
    main() 