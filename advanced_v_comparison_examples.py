#!/usr/bin/env python3
"""
Advanced examples for v-value comparison across different algorithms and configurations.
"""

from flexible_reward_plotter import plot_algorithm_v_comparison, find_available_configs
import os

def compare_algorithm_v_values(algorithm, base_config, v_values=None):
    """
    Compare v values for a specific algorithm and base configuration.
    """
    if v_values is None:
        v_values = [0.0, 1.0]
    
    print(f"\n{'='*60}")
    print(f"Comparing {algorithm.upper()} algorithm with v values: {v_values}")
    print(f"Base configuration: {base_config}")
    print(f"{'='*60}")
    
    # Check which configurations actually exist
    available_configs = find_available_configs(algorithm)
    existing_v_values = []
    
    for v in v_values:
        config = f"v={v}{base_config}"
        if config in available_configs:
            existing_v_values.append(v)
            print(f"✓ Found data for v={v}")
        else:
            print(f"✗ No data found for v={v}")
    
    if len(existing_v_values) >= 2:
        plot_algorithm_v_comparison(
            algorithm=algorithm,
            base_config=base_config,
            v_values=existing_v_values,
            show_stats=True
        )
        return True
    else:
        print(f"Not enough data to compare (found {len(existing_v_values)} configurations)")
        return False

def main():
    """
    Run various v-value comparison examples.
    """
    print("Advanced V-Value Comparison Examples")
    print("="*50)
    
    # Example 1: A2C with different configurations
    examples = [
        ("a2c", "M=1N=2"),
        ("a2c", "M=1N=4"),
        ("a2c", "M=1N=8"),
        ("ppo", "M=1N=2"),
        ("ppo", "M=1N=4"),
        ("ddpg", "M=1N=2"),
        ("td3", "M=1N=2"),
    ]
    
    successful_plots = 0
    
    for algorithm, base_config in examples:
        if compare_algorithm_v_values(algorithm, base_config):
            successful_plots += 1
    
    print(f"\n{'='*60}")
    print(f"Summary: Successfully created {successful_plots} comparison plots")
    print(f"{'='*60}")
    
    # Example 2: Create a comprehensive comparison with smoothing
    print("\nCreating smoothed comparison for A2C (M=1N=2)...")
    plot_algorithm_v_comparison(
        algorithm="a2c",
        base_config="M=1N=2",
        v_values=[0.0, 1.0],
        smooth_window=15,
        show_stats=True
    )

if __name__ == "__main__":
    main()
