import argparse
import os

import matplotlib.pyplot as plt
import numpy as np


def find_available_algorithms():
    """Find all available algorithms in the logs directory."""
    logs_dir = "logs"
    if not os.path.exists(logs_dir):
        return []

    algorithms = []
    for item in os.listdir(logs_dir):
        if os.path.isdir(os.path.join(logs_dir, item)):
            algorithms.append(item)

    return sorted(algorithms)


def find_available_configs(algorithm):
    """Find all available configurations for a given algorithm."""
    algorithm_dir = f"logs/{algorithm}"
    if not os.path.exists(algorithm_dir):
        return []

    configs = []
    for item in os.listdir(algorithm_dir):
        config_path = os.path.join(algorithm_dir, item)
        if os.path.isdir(config_path):
            # Check if episode_rewards.npy exists
            rewards_file = os.path.join(
                config_path, "evaluations", "episode_rewards.npy"
            )
            if os.path.exists(rewards_file):
                configs.append(item)

    return sorted(configs)


def find_available_metrics():
    """Find all available metrics in the evaluation files."""
    # Common metrics found in the evaluations directory
    return [
        "episode_rewards",
        "episode_delays",
        "episode_energies",
        "episode_battery_level",
        "episode_queue_length",
    ]


def get_metric_info(metric):
    """Get display information for different metrics."""
    metric_info = {
        "episode_rewards": {
            "ylabel": "Reward",
            "title_suffix": "Rewards",
            "file_suffix": "rewards",
        },
        "episode_delays": {
            "ylabel": "Delay",
            "title_suffix": "Delays",
            "file_suffix": "delays",
        },
        "episode_energies": {
            "ylabel": "Energy",
            "title_suffix": "Energy Consumption",
            "file_suffix": "energies",
        },
        "episode_battery_level": {
            "ylabel": "Battery Level",
            "title_suffix": "Battery Level",
            "file_suffix": "battery",
        },
        "episode_queue_length": {
            "ylabel": "Queue Length",
            "title_suffix": "Queue Length",
            "file_suffix": "queue",
        },
    }
    return metric_info.get(
        metric,
        {
            "ylabel": metric.replace("episode_", "").replace("_", " ").title(),
            "title_suffix": metric.replace("episode_", "").replace("_", " ").title(),
            "file_suffix": metric.replace("episode_", ""),
        },
    )


def plot_algorithms_comparison(
    algorithms=None, config="v=0.0M=1N=2", smooth_window=None, show_stats=True
):
    """
    Plot reward changes for multiple algorithms.

    Args:
        algorithms: List of algorithm names. If None, uses all available algorithms.
        config: Configuration string (e.g., "v=0.0M=1N=2")
        smooth_window: If provided, applies moving average smoothing
        show_stats: Whether to show statistics in the legend
    """

    if algorithms is None:
        algorithms = find_available_algorithms()
        # Filter out test algorithms
        algorithms = [alg for alg in algorithms if not alg.startswith("test")]

    plt.figure(figsize=(14, 10))
    colors = [
        "blue",
        "red",
        "green",
        "orange",
        "purple",
        "brown",
        "pink",
        "gray",
        "cyan",
        "magenta",
    ]
    data_loaded = False

    stats_info = []

    for i, algorithm in enumerate(algorithms):
        data_path = f"logs/{algorithm}/{config}/evaluations/episode_rewards.npy"

        if os.path.exists(data_path):
            try:
                rewards = np.load(data_path)
                episodes = np.arange(1, len(rewards) + 1)

                # Apply smoothing if requested
                if smooth_window and smooth_window > 1:
                    # Moving average smoothing
                    smoothed_rewards = np.convolve(
                        rewards, np.ones(smooth_window) / smooth_window, mode="valid"
                    )
                    smoothed_episodes = episodes[smooth_window - 1 :]

                    # Plot both original (faded) and smoothed
                    plt.plot(
                        episodes,
                        rewards,
                        color=colors[i % len(colors)],
                        alpha=0.3,
                        linewidth=1,
                    )
                    plt.plot(
                        smoothed_episodes,
                        smoothed_rewards,
                        color=colors[i % len(colors)],
                        label=f"{algorithm.upper()}",
                        linewidth=2,
                    )
                else:
                    plt.plot(
                        episodes,
                        rewards,
                        color=colors[i % len(colors)],
                        label=f"{algorithm.upper()}",
                        linewidth=2,
                        alpha=0.8,
                    )

                # Calculate statistics
                if show_stats:
                    mean_reward = np.mean(rewards)
                    max_reward = np.max(rewards)
                    final_reward = (
                        np.mean(rewards[-10:]) if len(rewards) >= 10 else rewards[-1]
                    )
                    stats_info.append(
                        f"{algorithm.upper()}: μ={mean_reward:.2f}, max={max_reward:.2f}, final={final_reward:.2f}"
                    )

                data_loaded = True
                print(f"Loaded data for {algorithm}: {len(rewards)} episodes")

            except Exception as e:
                print(f"Error loading data for {algorithm}: {e}")
        else:
            print(f"Data file not found for {algorithm}: {data_path}")

    if not data_loaded:
        print("No data was loaded. Please check the file paths.")
        return

    # Customize the plot
    plt.xlabel("Episode", fontsize=14)
    plt.ylabel("Reward", fontsize=14)

    title = f"Episode Rewards Comparison Across Algorithms\n(Configuration: {config})"
    if smooth_window:
        title += f"\n(Smoothed with window size {smooth_window})"
    plt.title(title, fontsize=16)

    plt.legend(fontsize=11, loc="best")
    plt.grid(True, alpha=0.3)

    # Add statistics as text if requested
    if show_stats and stats_info:
        stats_text = "\n".join(stats_info)
        plt.figtext(
            0.02,
            0.02,
            stats_text,
            fontsize=9,
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8),
        )

    plt.tight_layout()

    # Save the plot
    smooth_suffix = f"_smooth{smooth_window}" if smooth_window else ""
    output_path = f"results/rewards_comparison_{config.replace('=', '_').replace('.', '_')}{smooth_suffix}.png"
    plt.savefig(output_path, dpi=300, bbox_inches="tight")
    print(f"Plot saved as: {output_path}")


def plot_flexible_comparison(
    algorithm_configs=None,
    metric="episode_rewards",
    smooth_window=None,
    show_stats=True,
    custom_labels=None,
):
    """
    Flexible plotting function that can plot any combination of algorithms and configurations.

    Args:
        algorithm_configs: List of tuples (algorithm, config) or list of dicts with 'algorithm' and 'config' keys
                          Example: [("a2c", "v=0.0M=1N=2"), ("a2c", "v=1.0M=1N=2"), ("ppo", "v=1.0M=1N=2")]
                          Or: [{"algorithm": "a2c", "config": "v=0.0M=1N=2", "label": "A2C v=0.0"}]
        metric: Metric to plot (e.g., "episode_rewards", "episode_delays", "episode_energies")
        smooth_window: If provided, applies moving average smoothing
        show_stats: Whether to show statistics
        custom_labels: List of custom labels for each algorithm-config combination
    """

    if algorithm_configs is None:
        # Default: compare a2c v=0.0 and v=1.0
        algorithm_configs = [("a2c", "v=0.0M=1N=2"), ("a2c", "v=1.0M=1N=2")]

    # Normalize algorithm_configs to list of dicts
    normalized_configs = []
    for i, config_item in enumerate(algorithm_configs):
        if isinstance(config_item, tuple):
            algorithm, config = config_item
            label = (
                custom_labels[i]
                if custom_labels and i < len(custom_labels)
                else f"{algorithm.upper()} ({config})"
            )
            normalized_configs.append(
                {"algorithm": algorithm, "config": config, "label": label}
            )
        elif isinstance(config_item, dict):
            if "label" not in config_item:
                config_item["label"] = (
                    f"{config_item['algorithm'].upper()} ({config_item['config']})"
                )
            normalized_configs.append(config_item)

    # Get metric information
    metric_info = get_metric_info(metric)

    plt.figure(figsize=(14, 10))
    colors = [
        "blue",
        "red",
        "green",
        "orange",
        "purple",
        "brown",
        "pink",
        "gray",
        "cyan",
        "magenta",
    ]
    line_styles = ["-", "--", "-.", ":", "-", "--", "-.", ":", "-", "--"]
    data_loaded = False

    stats_info = []

    for i, config_dict in enumerate(normalized_configs):
        algorithm = config_dict["algorithm"]
        config = config_dict["config"]
        label = config_dict["label"]

        data_path = f"logs/{algorithm}/{config}/evaluations/{metric}.npy"

        if os.path.exists(data_path):
            try:
                data = np.load(data_path)
                episodes = np.arange(1, len(data) + 1)

                # Apply smoothing if requested
                if smooth_window and smooth_window > 1:
                    # Moving average smoothing
                    smoothed_data = np.convolve(
                        data, np.ones(smooth_window) / smooth_window, mode="valid"
                    )
                    smoothed_episodes = episodes[smooth_window - 1 :]

                    # Plot both original (faded) and smoothed
                    plt.plot(
                        episodes,
                        data,
                        color=colors[i % len(colors)],
                        alpha=0.3,
                        linewidth=1,
                        linestyle=line_styles[i % len(line_styles)],
                    )
                    plt.plot(
                        smoothed_episodes,
                        smoothed_data,
                        color=colors[i % len(colors)],
                        label=label,
                        linewidth=2,
                        linestyle=line_styles[i % len(line_styles)],
                    )
                else:
                    plt.plot(
                        episodes,
                        data,
                        color=colors[i % len(colors)],
                        label=label,
                        linewidth=2,
                        alpha=0.8,
                        linestyle=line_styles[i % len(line_styles)],
                    )

                # Calculate statistics
                if show_stats:
                    mean_value = np.mean(data)
                    max_value = np.max(data)
                    final_value = np.mean(data[-10:]) if len(data) >= 10 else data[-1]
                    stats_info.append(
                        f"{label}: μ={mean_value:.2f}, max={max_value:.2f}, final={final_value:.2f}"
                    )

                data_loaded = True
                print(f"Loaded data for {label}: {len(data)} episodes")

            except Exception as e:
                print(f"Error loading data for {label}: {e}")
        else:
            print(f"Data file not found for {label}: {data_path}")

    if not data_loaded:
        print("No data was loaded. Please check the file paths.")
        return

    # Customize the plot
    plt.xlabel("Episode", fontsize=14)
    plt.ylabel(metric_info["ylabel"], fontsize=14)

    title = f"Episode {metric_info['title_suffix']} Comparison"
    if smooth_window:
        title += f"\n(Smoothed with window size {smooth_window})"
    plt.title(title, fontsize=16)

    plt.legend(fontsize=11, loc="best")
    plt.grid(True, alpha=0.3)

    # Add statistics as text if requested
    if show_stats and stats_info:
        stats_text = "\n".join(stats_info)
        plt.figtext(
            0.02,
            0.02,
            stats_text,
            fontsize=9,
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8),
        )

    plt.tight_layout()

    # Save the plot
    smooth_suffix = f"_smooth{smooth_window}" if smooth_window else ""
    config_summary = "_".join(
        [
            f"{c['algorithm']}_{c['config'].replace('=', '_').replace('.', '_')}"
            for c in normalized_configs[:3]
        ]
    )
    if len(normalized_configs) > 3:
        config_summary += f"_and_{len(normalized_configs) - 3}_more"
    output_path = f"results/flexible_{metric_info['file_suffix']}_comparison_{config_summary}{smooth_suffix}.png"
    plt.savefig(output_path, dpi=300, bbox_inches="tight")
    print(f"Plot saved as: {output_path}")


def plot_algorithm_v_comparison(
    algorithm="a2c",
    base_config="M=1N=2",
    v_values=None,
    smooth_window=None,
    show_stats=True,
):
    """
    Plot reward changes for a single algorithm with different v values.

    Args:
        algorithm: Algorithm name (e.g., "a2c")
        base_config: Base configuration without v parameter (e.g., "M=1N=2")
        v_values: List of v values to compare. If None, uses [0.0, 1.0]
        smooth_window: If provided, applies moving average smoothing
        show_stats: Whether to show statistics in the legend
    """

    if v_values is None:
        v_values = [0.0, 1.0]

    plt.figure(figsize=(14, 10))
    colors = ["blue", "red", "green", "orange", "purple", "brown", "pink", "gray"]
    line_styles = ["-", "--", "-.", ":", "-", "--", "-.", ":"]
    data_loaded = False

    stats_info = []

    for i, v_value in enumerate(v_values):
        config = f"v={v_value}{base_config}"
        data_path = f"logs/{algorithm}/{config}/evaluations/episode_rewards.npy"

        if os.path.exists(data_path):
            try:
                rewards = np.load(data_path)
                episodes = np.arange(1, len(rewards) + 1)

                # Apply smoothing if requested
                if smooth_window and smooth_window > 1:
                    # Moving average smoothing
                    smoothed_rewards = np.convolve(
                        rewards, np.ones(smooth_window) / smooth_window, mode="valid"
                    )
                    smoothed_episodes = episodes[smooth_window - 1 :]

                    # Plot both original (faded) and smoothed
                    plt.plot(
                        episodes,
                        rewards,
                        color=colors[i % len(colors)],
                        alpha=0.3,
                        linewidth=1,
                        linestyle=line_styles[i % len(line_styles)],
                    )
                    plt.plot(
                        smoothed_episodes,
                        smoothed_rewards,
                        color=colors[i % len(colors)],
                        label=f"{algorithm.upper()} (v={v_value})",
                        linewidth=2,
                        linestyle=line_styles[i % len(line_styles)],
                    )
                else:
                    plt.plot(
                        episodes,
                        rewards,
                        color=colors[i % len(colors)],
                        label=f"{algorithm.upper()} (v={v_value})",
                        linewidth=2,
                        alpha=0.8,
                        linestyle=line_styles[i % len(line_styles)],
                    )

                # Calculate statistics
                if show_stats:
                    mean_reward = np.mean(rewards)
                    max_reward = np.max(rewards)
                    final_reward = (
                        np.mean(rewards[-10:]) if len(rewards) >= 10 else rewards[-1]
                    )
                    stats_info.append(
                        f"{algorithm.upper()} (v={v_value}): μ={mean_reward:.2f}, max={max_reward:.2f}, final={final_reward:.2f}"
                    )

                data_loaded = True
                print(
                    f"Loaded data for {algorithm} (v={v_value}): {len(rewards)} episodes"
                )

            except Exception as e:
                print(f"Error loading data for {algorithm} (v={v_value}): {e}")
        else:
            print(f"Data file not found for {algorithm} (v={v_value}): {data_path}")

    if not data_loaded:
        print("No data was loaded. Please check the file paths.")
        return

    # Customize the plot
    plt.xlabel("Episode", fontsize=14)
    plt.ylabel("Reward", fontsize=14)

    title = f"{algorithm.upper()} Algorithm: Comparison of Different V Values\n(Base Configuration: {base_config})"
    if smooth_window:
        title += f"\n(Smoothed with window size {smooth_window})"
    plt.title(title, fontsize=16)

    plt.legend(fontsize=11, loc="best")
    plt.grid(True, alpha=0.3)

    # Add statistics as text if requested
    if show_stats and stats_info:
        stats_text = "\n".join(stats_info)
        plt.figtext(
            0.02,
            0.02,
            stats_text,
            fontsize=9,
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8),
        )

    plt.tight_layout()

    # Save the plot
    v_values_str = "_".join([str(v).replace(".", "_") for v in v_values])
    smooth_suffix = f"_smooth{smooth_window}" if smooth_window else ""
    output_path = f"results/{algorithm}_v_comparison_{base_config.replace('=', '_')}_v{v_values_str}{smooth_suffix}.png"
    plt.savefig(output_path, dpi=300, bbox_inches="tight")
    print(f"Plot saved as: {output_path}")


def list_available_data():
    """List all available algorithms and their configurations."""
    print("Available algorithms and configurations:")
    print("=" * 50)

    algorithms = find_available_algorithms()
    for algorithm in algorithms:
        configs = find_available_configs(algorithm)
        print(f"\n{algorithm.upper()}:")
        for config in configs:
            print(f"  - {config}")

    print("\nAvailable metrics:")
    metrics = find_available_metrics()
    for metric in metrics:
        print(f"  - {metric}")


def parse_algorithm_configs(config_strings):
    """
    Parse algorithm-config strings into list of tuples.

    Args:
        config_strings: List of strings in format "algorithm:config" or "algorithm:config:label"
                       Example: ["a2c:v=0.0M=1N=2", "a2c:v=1.0M=1N=2:A2C v=1.0", "ppo:v=1.0M=1N=2"]

    Returns:
        List of dicts with algorithm, config, and optional label
    """
    result = []
    for config_str in config_strings:
        parts = config_str.split(":")
        if len(parts) >= 2:
            algorithm = parts[0]
            config = parts[1]
            label = parts[2] if len(parts) > 2 else f"{algorithm.upper()} ({config})"
            result.append({"algorithm": algorithm, "config": config, "label": label})
        else:
            print(
                f"Warning: Invalid config format '{config_str}'. Expected 'algorithm:config' or 'algorithm:config:label'"
            )

    return result


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Flexible plotting tool for RL algorithm comparisons"
    )

    # Mode selection
    parser.add_argument(
        "--mode",
        choices=["algorithms", "v-comparison", "flexible"],
        default="algorithms",
        help="Plotting mode: algorithms (compare algorithms with same config), v-comparison (compare v values for one algorithm), flexible (custom combinations)",
    )

    # Common arguments
    parser.add_argument(
        "--smooth", type=int, help="Moving average window size for smoothing"
    )
    parser.add_argument(
        "--list",
        action="store_true",
        help="List available algorithms, configurations, and metrics",
    )
    parser.add_argument("--no-stats", action="store_true", help="Don't show statistics")
    parser.add_argument(
        "--metric",
        default="episode_rewards",
        help="Metric to plot (default: episode_rewards). Use --list to see available metrics",
    )

    # Arguments for algorithms mode
    parser.add_argument("--algorithms", nargs="+", help="List of algorithms to compare")
    parser.add_argument("--config", default="v=0.0M=1N=2", help="Configuration to use")

    # Arguments for v-value comparison mode
    parser.add_argument(
        "--algorithm",
        default="a2c",
        help="Algorithm to use for v-value comparison (default: a2c)",
    )
    parser.add_argument(
        "--base-config",
        default="M=1N=2",
        help="Base configuration without v parameter (default: M=1N=2)",
    )
    parser.add_argument(
        "--v-values",
        nargs="+",
        type=float,
        default=[0.0, 1.0],
        help="List of v values to compare (default: 0.0 1.0)",
    )

    # Arguments for flexible mode
    parser.add_argument(
        "--configs",
        nargs="+",
        help="Algorithm-config combinations in format 'algorithm:config' or 'algorithm:config:label'. Example: a2c:v=0.0M=1N=2 a2c:v=1.0M=1N=2:A2C_v1.0 ppo:v=1.0M=1N=2",
    )

    args = parser.parse_args()

    if args.list:
        list_available_data()
    elif args.mode == "v-comparison":
        plot_algorithm_v_comparison(
            algorithm=args.algorithm,
            base_config=args.base_config,
            v_values=args.v_values,
            smooth_window=args.smooth,
            show_stats=not args.no_stats,
        )
    elif args.mode == "flexible":
        if not args.configs:
            print("Error: --configs is required for flexible mode")
            print(
                "Example: --mode flexible --configs a2c:v=0.0M=1N=2 a2c:v=1.0M=1N=2:A2C_v1.0 ppo:v=1.0M=1N=2"
            )
            exit(1)

        algorithm_configs = parse_algorithm_configs(args.configs)
        if not algorithm_configs:
            print("Error: No valid algorithm-config combinations provided")
            exit(1)

        plot_flexible_comparison(
            algorithm_configs=algorithm_configs,
            metric=args.metric,
            smooth_window=args.smooth,
            show_stats=not args.no_stats,
        )
    else:  # algorithms mode
        # Update the original function to support different metrics
        if args.metric != "episode_rewards":
            # Use flexible comparison for non-reward metrics
            if args.algorithms:
                algorithm_configs = [(alg, args.config) for alg in args.algorithms]
            else:
                # Use all available algorithms
                all_algorithms = find_available_algorithms()
                all_algorithms = [
                    alg for alg in all_algorithms if not alg.startswith("test")
                ]
                algorithm_configs = [(alg, args.config) for alg in all_algorithms]

            plot_flexible_comparison(
                algorithm_configs=algorithm_configs,
                metric=args.metric,
                smooth_window=args.smooth,
                show_stats=not args.no_stats,
            )
        else:
            # Use original function for rewards
            plot_algorithms_comparison(
                algorithms=args.algorithms,
                config=args.config,
                smooth_window=args.smooth,
                show_stats=not args.no_stats,
            )
