import argparse
import os

import matplotlib.pyplot as plt
import numpy as np


def find_available_algorithms():
    """Find all available algorithms in the logs directory."""
    logs_dir = "logs"
    if not os.path.exists(logs_dir):
        return []

    algorithms = []
    for item in os.listdir(logs_dir):
        if os.path.isdir(os.path.join(logs_dir, item)):
            algorithms.append(item)

    return sorted(algorithms)


def find_available_configs(algorithm):
    """Find all available configurations for a given algorithm."""
    algorithm_dir = f"logs/{algorithm}"
    if not os.path.exists(algorithm_dir):
        return []

    configs = []
    for item in os.listdir(algorithm_dir):
        config_path = os.path.join(algorithm_dir, item)
        if os.path.isdir(config_path):
            # Check if episode_rewards.npy exists
            rewards_file = os.path.join(
                config_path, "evaluations", "episode_rewards.npy"
            )
            if os.path.exists(rewards_file):
                configs.append(item)

    return sorted(configs)


def plot_algorithms_comparison(
    algorithms=None, config="v=0.0M=1N=2", smooth_window=None, show_stats=True
):
    """
    Plot reward changes for multiple algorithms.

    Args:
        algorithms: List of algorithm names. If None, uses all available algorithms.
        config: Configuration string (e.g., "v=0.0M=1N=2")
        smooth_window: If provided, applies moving average smoothing
        show_stats: Whether to show statistics in the legend
    """

    if algorithms is None:
        algorithms = find_available_algorithms()
        # Filter out test algorithms
        algorithms = [alg for alg in algorithms if not alg.startswith("test")]

    plt.figure(figsize=(14, 10))
    colors = [
        "blue",
        "red",
        "green",
        "orange",
        "purple",
        "brown",
        "pink",
        "gray",
        "cyan",
        "magenta",
    ]
    data_loaded = False

    stats_info = []

    for i, algorithm in enumerate(algorithms):
        data_path = f"logs/{algorithm}/{config}/evaluations/episode_rewards.npy"

        if os.path.exists(data_path):
            try:
                rewards = np.load(data_path)
                episodes = np.arange(1, len(rewards) + 1)

                # Apply smoothing if requested
                if smooth_window and smooth_window > 1:
                    # Moving average smoothing
                    smoothed_rewards = np.convolve(
                        rewards, np.ones(smooth_window) / smooth_window, mode="valid"
                    )
                    smoothed_episodes = episodes[smooth_window - 1 :]

                    # Plot both original (faded) and smoothed
                    plt.plot(
                        episodes,
                        rewards,
                        color=colors[i % len(colors)],
                        alpha=0.3,
                        linewidth=1,
                    )
                    plt.plot(
                        smoothed_episodes,
                        smoothed_rewards,
                        color=colors[i % len(colors)],
                        label=f"{algorithm.upper()}",
                        linewidth=2,
                    )
                else:
                    plt.plot(
                        episodes,
                        rewards,
                        color=colors[i % len(colors)],
                        label=f"{algorithm.upper()}",
                        linewidth=2,
                        alpha=0.8,
                    )

                # Calculate statistics
                if show_stats:
                    mean_reward = np.mean(rewards)
                    max_reward = np.max(rewards)
                    final_reward = (
                        np.mean(rewards[-10:]) if len(rewards) >= 10 else rewards[-1]
                    )
                    stats_info.append(
                        f"{algorithm.upper()}: μ={mean_reward:.2f}, max={max_reward:.2f}, final={final_reward:.2f}"
                    )

                data_loaded = True
                print(f"Loaded data for {algorithm}: {len(rewards)} episodes")

            except Exception as e:
                print(f"Error loading data for {algorithm}: {e}")
        else:
            print(f"Data file not found for {algorithm}: {data_path}")

    if not data_loaded:
        print("No data was loaded. Please check the file paths.")
        return

    # Customize the plot
    plt.xlabel("Episode", fontsize=14)
    plt.ylabel("Reward", fontsize=14)

    title = f"Episode Rewards Comparison Across Algorithms\n(Configuration: {config})"
    if smooth_window:
        title += f"\n(Smoothed with window size {smooth_window})"
    plt.title(title, fontsize=16)

    plt.legend(fontsize=11, loc="best")
    plt.grid(True, alpha=0.3)

    # Add statistics as text if requested
    if show_stats and stats_info:
        stats_text = "\n".join(stats_info)
        plt.figtext(
            0.02,
            0.02,
            stats_text,
            fontsize=9,
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8),
        )

    plt.tight_layout()

    # Save the plot
    smooth_suffix = f"_smooth{smooth_window}" if smooth_window else ""
    output_path = f"rewards_comparison_{config.replace('=', '_').replace('.', '_')}{smooth_suffix}.png"
    plt.savefig(output_path, dpi=300, bbox_inches="tight")
    print(f"Plot saved as: {output_path}")

    plt.show()


def plot_algorithm_v_comparison(
    algorithm="a2c",
    base_config="M=1N=2",
    v_values=None,
    smooth_window=None,
    show_stats=True,
):
    """
    Plot reward changes for a single algorithm with different v values.

    Args:
        algorithm: Algorithm name (e.g., "a2c")
        base_config: Base configuration without v parameter (e.g., "M=1N=2")
        v_values: List of v values to compare. If None, uses [0.0, 1.0]
        smooth_window: If provided, applies moving average smoothing
        show_stats: Whether to show statistics in the legend
    """

    if v_values is None:
        v_values = [0.0, 1.0]

    plt.figure(figsize=(14, 10))
    colors = ["blue", "red", "green", "orange", "purple", "brown", "pink", "gray"]
    line_styles = ["-", "--", "-.", ":", "-", "--", "-.", ":"]
    data_loaded = False

    stats_info = []

    for i, v_value in enumerate(v_values):
        config = f"v={v_value}{base_config}"
        data_path = f"logs/{algorithm}/{config}/evaluations/episode_rewards.npy"

        if os.path.exists(data_path):
            try:
                rewards = np.load(data_path)
                episodes = np.arange(1, len(rewards) + 1)

                # Apply smoothing if requested
                if smooth_window and smooth_window > 1:
                    # Moving average smoothing
                    smoothed_rewards = np.convolve(
                        rewards, np.ones(smooth_window) / smooth_window, mode="valid"
                    )
                    smoothed_episodes = episodes[smooth_window - 1 :]

                    # Plot both original (faded) and smoothed
                    plt.plot(
                        episodes,
                        rewards,
                        color=colors[i % len(colors)],
                        alpha=0.3,
                        linewidth=1,
                        linestyle=line_styles[i % len(line_styles)],
                    )
                    plt.plot(
                        smoothed_episodes,
                        smoothed_rewards,
                        color=colors[i % len(colors)],
                        label=f"{algorithm.upper()} (v={v_value})",
                        linewidth=2,
                        linestyle=line_styles[i % len(line_styles)],
                    )
                else:
                    plt.plot(
                        episodes,
                        rewards,
                        color=colors[i % len(colors)],
                        label=f"{algorithm.upper()} (v={v_value})",
                        linewidth=2,
                        alpha=0.8,
                        linestyle=line_styles[i % len(line_styles)],
                    )

                # Calculate statistics
                if show_stats:
                    mean_reward = np.mean(rewards)
                    max_reward = np.max(rewards)
                    final_reward = (
                        np.mean(rewards[-10:]) if len(rewards) >= 10 else rewards[-1]
                    )
                    stats_info.append(
                        f"{algorithm.upper()} (v={v_value}): μ={mean_reward:.2f}, max={max_reward:.2f}, final={final_reward:.2f}"
                    )

                data_loaded = True
                print(
                    f"Loaded data for {algorithm} (v={v_value}): {len(rewards)} episodes"
                )

            except Exception as e:
                print(f"Error loading data for {algorithm} (v={v_value}): {e}")
        else:
            print(f"Data file not found for {algorithm} (v={v_value}): {data_path}")

    if not data_loaded:
        print("No data was loaded. Please check the file paths.")
        return

    # Customize the plot
    plt.xlabel("Episode", fontsize=14)
    plt.ylabel("Reward", fontsize=14)

    title = f"{algorithm.upper()} Algorithm: Comparison of Different V Values\n(Base Configuration: {base_config})"
    if smooth_window:
        title += f"\n(Smoothed with window size {smooth_window})"
    plt.title(title, fontsize=16)

    plt.legend(fontsize=11, loc="best")
    plt.grid(True, alpha=0.3)

    # Add statistics as text if requested
    if show_stats and stats_info:
        stats_text = "\n".join(stats_info)
        plt.figtext(
            0.02,
            0.02,
            stats_text,
            fontsize=9,
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8),
        )

    plt.tight_layout()

    # Save the plot
    v_values_str = "_".join([str(v).replace(".", "_") for v in v_values])
    smooth_suffix = f"_smooth{smooth_window}" if smooth_window else ""
    output_path = f"{algorithm}_v_comparison_{base_config.replace('=', '_')}_v{v_values_str}{smooth_suffix}.png"
    plt.savefig(output_path, dpi=300, bbox_inches="tight")
    print(f"Plot saved as: {output_path}")

    plt.show()


def list_available_data():
    """List all available algorithms and their configurations."""
    print("Available algorithms and configurations:")
    print("=" * 50)

    algorithms = find_available_algorithms()
    for algorithm in algorithms:
        configs = find_available_configs(algorithm)
        print(f"\n{algorithm.upper()}:")
        for config in configs:
            print(f"  - {config}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Plot reward comparisons across RL algorithms"
    )
    parser.add_argument("--algorithms", nargs="+", help="List of algorithms to compare")
    parser.add_argument("--config", default="v=0.0M=1N=2", help="Configuration to use")
    parser.add_argument(
        "--smooth", type=int, help="Moving average window size for smoothing"
    )
    parser.add_argument(
        "--list",
        action="store_true",
        help="List available algorithms and configurations",
    )
    parser.add_argument("--no-stats", action="store_true", help="Don't show statistics")

    # New arguments for v-value comparison
    parser.add_argument(
        "--v-comparison",
        action="store_true",
        help="Compare different v values for a single algorithm",
    )
    parser.add_argument(
        "--algorithm",
        default="a2c",
        help="Algorithm to use for v-value comparison (default: a2c)",
    )
    parser.add_argument(
        "--base-config",
        default="M=1N=2",
        help="Base configuration without v parameter (default: M=1N=2)",
    )
    parser.add_argument(
        "--v-values",
        nargs="+",
        type=float,
        default=[0.0, 1.0],
        help="List of v values to compare (default: 0.0 1.0)",
    )

    args = parser.parse_args()

    if args.list:
        list_available_data()
    elif args.v_comparison:
        plot_algorithm_v_comparison(
            algorithm=args.algorithm,
            base_config=args.base_config,
            v_values=args.v_values,
            smooth_window=args.smooth,
            show_stats=not args.no_stats,
        )
    else:
        plot_algorithms_comparison(
            algorithms=args.algorithms,
            config=args.config,
            smooth_window=args.smooth,
            show_stats=not args.no_stats,
        )
