#!/usr/bin/bash
train_pys=("train_a2c.py" "train_ppo.py" "train_ddpg.py" "train_td3.py")
v_list=(0 1)
m_list=(1)
n_list=(2 4 6 8 10 12)
#lr_list=(0.00001 0.00002 0.00005 0.0001 0.0002 0.0005 0.001 0.002 0.005)
lr_list=(0.0002)
runPy() {
    for v in "${v_list[@]}"
    do
        for m in "${m_list[@]}"
        do
            for n in "${n_list[@]}"
            do
                if [ $n -gt $m ]
                then
                    for lr in "${lr_list[@]}"
                    do
                        echo "开始执行 $1, v=$v, M=$m, N=$n"
                        python $1 --v=$v --M=$m --N=$n
                    done
                fi
            done
        done
    done
}

for train_py in "${train_pys[@]}"
do
    runPy $train_py
done