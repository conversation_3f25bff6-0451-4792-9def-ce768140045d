# V值比较功能使用说明

## 概述

`flexible_reward_plotter.py` 现在支持比较同一算法在不同 v 值下的表现。这个功能特别适用于分析参数 v 对算法性能的影响。

## 新增功能

### 1. V值比较函数

```python
plot_algorithm_v_comparison(
    algorithm="a2c",           # 算法名称
    base_config="M=1N=2",      # 基础配置（不包含v参数）
    v_values=[0.0, 1.0],       # 要比较的v值列表
    smooth_window=None,        # 平滑窗口大小（可选）
    show_stats=True            # 是否显示统计信息
)
```

### 2. 命令行使用方式

#### 基本用法
```bash
# 比较A2C算法在v=0.0和v=1.0下的表现
python flexible_reward_plotter.py --v-comparison --algorithm a2c --base-config "M=1N=2" --v-values 0.0 1.0
```

#### 带平滑功能
```bash
# 添加10点移动平均平滑
python flexible_reward_plotter.py --v-comparison --algorithm a2c --base-config "M=1N=2" --v-values 0.0 1.0 --smooth 10
```

#### 比较其他算法
```bash
# 比较PPO算法
python flexible_reward_plotter.py --v-comparison --algorithm ppo --base-config "M=1N=2" --v-values 0.0 1.0

# 比较DDPG算法
python flexible_reward_plotter.py --v-comparison --algorithm ddpg --base-config "M=1N=2" --v-values 0.0 1.0

# 比较TD3算法
python flexible_reward_plotter.py --v-comparison --algorithm td3 --base-config "M=1N=2" --v-values 0.0 1.0
```

#### 比较更多v值
```bash
# 如果有更多v值的数据，可以同时比较
python flexible_reward_plotter.py --v-comparison --algorithm a2c --base-config "M=1N=2" --v-values 0.0 0.5 1.0
```

## 输出文件

生成的图片文件命名格式：
- `{algorithm}_v_comparison_{base_config}_v{v_values}.png`
- 例如：`a2c_v_comparison_M_1N_2_v0_0_1_0.png`
- 带平滑：`a2c_v_comparison_M_1N_2_v0_0_1_0_smooth10.png`

## 图表特性

1. **不同颜色和线型**：每个v值使用不同的颜色和线型进行区分
2. **统计信息**：显示每个配置的平均奖励、最大奖励和最终奖励
3. **平滑选项**：可选的移动平均平滑来减少噪声
4. **高质量输出**：300 DPI的PNG格式图片

## 示例脚本

### 1. 基本使用示例
```python
from flexible_reward_plotter import plot_algorithm_v_comparison

# 比较A2C在不同v值下的表现
plot_algorithm_v_comparison(
    algorithm="a2c",
    base_config="M=1N=2",
    v_values=[0.0, 1.0],
    show_stats=True
)
```

### 2. 带平滑的比较
```python
# 使用15点移动平均平滑
plot_algorithm_v_comparison(
    algorithm="a2c",
    base_config="M=1N=2",
    v_values=[0.0, 1.0],
    smooth_window=15,
    show_stats=True
)
```

## 数据要求

确保以下路径存在对应的数据文件：
- `logs/{algorithm}/v={v_value}{base_config}/evaluations/episode_rewards.npy`

例如：
- `logs/a2c/v=0.0M=1N=2/evaluations/episode_rewards.npy`
- `logs/a2c/v=1.0M=1N=2/evaluations/episode_rewards.npy`

## 故障排除

1. **数据文件不存在**：检查日志目录结构和文件路径
2. **没有足够的数据**：确保至少有两个不同v值的数据文件
3. **图片不显示**：检查matplotlib后端设置

## 原有功能保持不变

所有原有的多算法比较功能仍然可用：
```bash
# 原有的多算法比较
python flexible_reward_plotter.py --config "v=0.0M=1N=2"

# 列出可用数据
python flexible_reward_plotter.py --list
```
