#!/usr/bin/env python3
"""
Workload Analysis Plotter - 任务计算量分析绘图工具

绘制x轴为不同任务计算量，y轴为时延、能耗等指标的折线图。
支持多个算法和不同v,M等条件的比较。
"""

import argparse
import os
import re
from pathlib import Path

import matplotlib.pyplot as plt
import numpy as np


def parse_config_parameters(config):
    """
    从配置字符串中解析参数
    
    Args:
        config: 配置字符串，如 "v=1.0M=4N=8"
    
    Returns:
        dict: 包含解析出的参数
    """
    params = {}
    
    # 解析v参数
    v_match = re.search(r'v=([0-9.]+)', config)
    if v_match:
        params['v'] = float(v_match.group(1))
    
    # 解析M参数（设备数量）
    m_match = re.search(r'M=([0-9]+)', config)
    if m_match:
        params['M'] = int(m_match.group(1))
    
    # 解析N参数（任务计算量）
    n_match = re.search(r'N=([0-9]+)', config)
    if n_match:
        params['N'] = int(n_match.group(1))
    
    return params


def find_configs_by_pattern(algorithm, v_value=None, m_value=None):
    """
    根据模式查找配置
    
    Args:
        algorithm: 算法名称
        v_value: v值过滤条件
        m_value: M值过滤条件
    
    Returns:
        list: 匹配的配置列表，每个元素包含config和N值
    """
    algorithm_dir = f"logs/{algorithm}"
    if not os.path.exists(algorithm_dir):
        return []
    
    configs = []
    for item in os.listdir(algorithm_dir):
        config_path = os.path.join(algorithm_dir, item)
        if os.path.isdir(config_path):
            # 检查是否有评估数据
            eval_dir = os.path.join(config_path, "evaluations")
            if not os.path.exists(eval_dir):
                continue
            
            # 解析配置参数
            params = parse_config_parameters(item)
            
            # 应用过滤条件
            if v_value is not None and params.get('v') != v_value:
                continue
            if m_value is not None and params.get('M') != m_value:
                continue
            
            # 必须有N参数
            if 'N' not in params:
                continue
            
            configs.append({
                'config': item,
                'N': params['N'],
                'params': params
            })
    
    # 按N值排序
    configs.sort(key=lambda x: x['N'])
    return configs


def plot_workload_analysis(
    algorithm_conditions,
    metric="episode_delays",
    show_stats=True,
    smooth_window=None
):
    """
    绘制任务计算量分析图
    
    Args:
        algorithm_conditions: 算法条件列表，每个元素为dict包含algorithm, v, M, label等
        metric: 要分析的指标
        show_stats: 是否显示统计信息
        smooth_window: 平滑窗口大小
    """
    
    plt.figure(figsize=(12, 8))
    colors = ["blue", "red", "green", "orange", "purple", "brown", "pink", "gray", "cyan", "magenta"]
    markers = ["o", "s", "^", "D", "v", "<", ">", "p", "*", "h"]
    
    # 获取指标信息
    metric_info = {
        "episode_delays": {"ylabel": "Delay", "title": "Delay vs Task Workload"},
        "episode_energies": {"ylabel": "Energy", "title": "Energy vs Task Workload"},
        "episode_rewards": {"ylabel": "Reward", "title": "Reward vs Task Workload"},
        "episode_battery_level": {"ylabel": "Battery Level", "title": "Battery Level vs Task Workload"},
        "episode_queue_length": {"ylabel": "Queue Length", "title": "Queue Length vs Task Workload"}
    }
    
    info = metric_info.get(metric, {"ylabel": metric, "title": f"{metric} vs Task Workload"})
    
    data_loaded = False
    
    for i, condition in enumerate(algorithm_conditions):
        algorithm = condition['algorithm']
        v_value = condition.get('v')
        m_value = condition.get('M')
        label = condition.get('label', f"{algorithm.upper()} v={v_value} M={m_value}")
        
        # 查找匹配的配置
        configs = find_configs_by_pattern(algorithm, v_value, m_value)
        
        if not configs:
            print(f"No configs found for {label}")
            continue
        
        workloads = []
        metric_values = []
        
        for config_info in configs:
            config = config_info['config']
            n_value = config_info['N']
            
            # 加载数据
            data_path = f"logs/{algorithm}/{config}/evaluations/{metric}.npy"
            
            if os.path.exists(data_path):
                try:
                    data = np.load(data_path)
                    
                    # 计算最终性能（最后10个episode的平均值）
                    if len(data) >= 10:
                        final_value = np.mean(data[-10:])
                    else:
                        final_value = np.mean(data)
                    
                    workloads.append(n_value)
                    metric_values.append(final_value)
                    
                except Exception as e:
                    print(f"Error loading {data_path}: {e}")
        
        if workloads:
            # 按任务计算量排序
            sorted_data = sorted(zip(workloads, metric_values))
            workloads, metric_values = zip(*sorted_data)
            
            plt.plot(
                workloads,
                metric_values,
                color=colors[i % len(colors)],
                marker=markers[i % len(markers)],
                label=label,
                linewidth=2,
                markersize=8,
                alpha=0.8
            )
            
            data_loaded = True
            print(f"Loaded data for {label}: {len(workloads)} workload configurations")
    
    if not data_loaded:
        print("No data was loaded. Please check the conditions.")
        return
    
    # 自定义图表
    plt.xlabel("Task Workload (N)", fontsize=14)
    plt.ylabel(info["ylabel"], fontsize=14)
    plt.title(info["title"], fontsize=16)
    plt.legend(fontsize=11, loc="best")
    plt.grid(True, alpha=0.3)
    
    # 设置x轴为整数刻度
    plt.gca().set_xticks(sorted(set([x for line in plt.gca().get_lines() for x in line.get_xdata()])))
    
    plt.tight_layout()
    
    # 保存图片
    condition_summary = "_".join([f"{c['algorithm']}_v{c.get('v', 'X')}_M{c.get('M', 'X')}" for c in algorithm_conditions[:3]])
    if len(algorithm_conditions) > 3:
        condition_summary += f"_and_{len(algorithm_conditions)-3}_more"
    
    output_path = f"workload_analysis_{metric.replace('episode_', '')}_{condition_summary}.png"
    plt.savefig(output_path, dpi=300, bbox_inches="tight")
    print(f"Plot saved as: {output_path}")
    
    plt.show()


def main():
    parser = argparse.ArgumentParser(description="Workload Analysis Plotter")
    
    parser.add_argument(
        "--conditions",
        nargs="+",
        required=True,
        help="Algorithm conditions in format 'algorithm:v:M:label'. Example: a2c:1.0:2:A2C_v1.0_M2"
    )
    
    parser.add_argument(
        "--metric",
        default="episode_delays",
        help="Metric to analyze (default: episode_delays)"
    )
    
    parser.add_argument(
        "--smooth",
        type=int,
        help="Smoothing window size"
    )
    
    parser.add_argument(
        "--no-stats",
        action="store_true",
        help="Don't show statistics"
    )
    
    args = parser.parse_args()
    
    # 解析条件
    algorithm_conditions = []
    for condition_str in args.conditions:
        parts = condition_str.split(":")
        if len(parts) >= 3:
            algorithm = parts[0]
            v_value = float(parts[1]) if parts[1] != 'X' else None
            m_value = int(parts[2]) if parts[2] != 'X' else None
            label = parts[3] if len(parts) > 3 else f"{algorithm.upper()} v={v_value} M={m_value}"
            
            algorithm_conditions.append({
                'algorithm': algorithm,
                'v': v_value,
                'M': m_value,
                'label': label
            })
        else:
            print(f"Warning: Invalid condition format '{condition_str}'. Expected 'algorithm:v:M' or 'algorithm:v:M:label'")
    
    if not algorithm_conditions:
        print("No valid conditions provided")
        return
    
    plot_workload_analysis(
        algorithm_conditions=algorithm_conditions,
        metric=args.metric,
        show_stats=not args.no_stats,
        smooth_window=args.smooth
    )


if __name__ == "__main__":
    main()
