# 绘图工具使用指南

## 概述

本项目提供了两个核心绘图工具，用于分析强化学习算法在无人机任务卸载场景中的性能。

## 参数说明

- **M**: 无人机数量 (UAV数量)
- **N**: 终端数量 (UE数量)
- **D_total**: 任务计算量 (每个周期内的总数据量)
- **v**: <PERSON><PERSON><PERSON><PERSON>权重参数 (0.0表示不使用Lyapunov优化，1.0表示使用)

## 核心绘图工具

### 1. `flexible_reward_plotter.py` - 灵活绘图工具 ⭐ 主要工具

支持多种绘图模式和指标的通用工具。

**三种模式**：
- `algorithms`: 比较多个算法在相同配置下的表现
- `v-comparison`: 比较单个算法在不同v值下的表现
- `flexible`: 任意组合算法和配置

**输出目录**: `results/algorithm_comparison/`, `results/flexible_comparison/{metric}/`, `results/v_comparison/`

**使用示例**：
```bash
# 混合算法配置比较
python flexible_reward_plotter.py --mode flexible --configs \
  a2c:v=0.0M=1N=2:"A2C v=0.0" \
  a2c:v=1.0M=1N=2:"A2C v=1.0" \
  ppo:v=1.0M=1N=2:"PPO v=1.0" \
  ddpg:v=1.0M=1N=2:"DDPG v=1.0"

# 绘制延迟指标
python flexible_reward_plotter.py --mode flexible --metric episode_delays --configs \
  a2c:v=0.0M=1N=2 a2c:v=1.0M=1N=2

# V值比较
python flexible_reward_plotter.py --mode v-comparison --algorithm a2c --base-config "M=1N=2"
```

### 2. `parameter_analysis_plotter.py` - 参数分析工具 ⭐ 扩展性分析

分析参数变化对性能的影响，绘制折线图。

**两种分析类型**：
- `device`: 无人机数量分析 (X轴=M，固定N)
- `workload`: 终端数量分析 (X轴=N，固定M)

**输出目录**: `results/parameter_analysis/{analysis_type}_analysis/{metric}/`

**使用示例**：
```bash
# 无人机数量对延迟的影响（固定终端数量N=4）
python parameter_analysis_plotter.py --analysis device \
  --conditions a2c:1.0:4:"A2C v=1.0" a2c:0.0:4:"A2C v=0.0" \
  --metric episode_delays

# 终端数量对能耗的影响（固定无人机数量M=1）
python parameter_analysis_plotter.py --analysis workload \
  --conditions a2c:1.0:1:"A2C v=1.0" a2c:0.0:1:"A2C v=0.0" \
  --metric episode_energies
```

## 可用指标

- `episode_rewards` - 奖励
- `episode_delays` - 延迟 (秒)
- `episode_energies` - 能耗 (焦耳)
- `episode_battery_level` - 电池电量 (焦耳)
- `episode_queue_length` - 队列长度

## 输出目录结构

所有图片都保存在 `results/` 目录下的对应子文件夹中：

```
results/
├── algorithm_comparison/          # 算法比较图
├── flexible_comparison/           # 灵活比较图
│   ├── rewards/                  # 奖励指标
│   ├── delays/                   # 延迟指标
│   ├── energies/                 # 能耗指标
│   └── ...
├── v_comparison/                  # V值比较图
└── parameter_analysis/            # 参数分析图
    ├── device_analysis/          # 无人机数量分析
    │   ├── delays/
    │   ├── energies/
    │   └── ...
    └── workload_analysis/        # 终端数量分析
        ├── delays/
        ├── energies/
        └── ...
```

## 实际应用场景

### 场景1: 算法性能对比
```bash
# 比较不同算法在相同配置下的表现
python flexible_reward_plotter.py --mode algorithms \
  --algorithms a2c ppo ddpg td3 \
  --config "v=1.0M=2N=4" \
  --metric episode_delays
```

### 场景2: 参数敏感性分析
```bash
# 分析v参数对A2C性能的影响
python flexible_reward_plotter.py --mode v-comparison \
  --algorithm a2c \
  --base-config "M=2N=4" \
  --v-values 0.0 1.0
```

### 场景3: 扩展性分析
```bash
# 无人机扩展性分析
python parameter_analysis_plotter.py --analysis device \
  --conditions a2c:1.0:8:"A2C" ppo:1.0:8:"PPO" \
  --metric episode_delays

# 终端扩展性分析
python parameter_analysis_plotter.py --analysis workload \
  --conditions a2c:1.0:2:"A2C" ppo:1.0:2:"PPO" \
  --metric episode_delays
```

### 场景4: 混合条件比较
```bash
# 复杂混合比较
python flexible_reward_plotter.py --mode flexible --configs \
  a2c:v=0.0M=1N=2:"A2C Conservative" \
  a2c:v=1.0M=1N=2:"A2C Aggressive" \
  ppo:v=1.0M=1N=2:"PPO" \
  ddpg:v=1.0M=1N=2:"DDPG" \
  td3:v=1.0M=1N=2:"TD3"
```

## 快速开始

1. **查看可用数据**：
   ```bash
   python flexible_reward_plotter.py --list
   ```

2. **基本算法比较**：
   ```bash
   python flexible_reward_plotter.py --mode algorithms --config "v=1.0M=1N=2"
   ```

3. **参数分析**：
   ```bash
   python parameter_analysis_plotter.py --analysis device --conditions a2c:1.0:4 --metric episode_delays
   ```

4. **自定义比较**：
   ```bash
   python flexible_reward_plotter.py --mode flexible --configs a2c:v=0.0M=1N=2 a2c:v=1.0M=1N=2
   ```

## 工具选择建议

- **Episode级别分析**: 使用 `flexible_reward_plotter.py`
- **参数扩展性分析**: 使用 `parameter_analysis_plotter.py`
- **混合条件比较**: 使用 `flexible_reward_plotter.py` 的 flexible 模式

## 注意事项

1. 确保数据文件存在于正确的路径
2. 使用 `--list` 参数查看可用的算法、配置和指标
3. 所有工具都支持平滑功能 (`--smooth`)
4. 可以使用 `--no-stats` 隐藏统计信息
5. 输出图片自动保存到对应的子文件夹中
