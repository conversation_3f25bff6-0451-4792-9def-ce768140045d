# 参数分析绘图工具使用指南

## 概述

新增了三个专门的参数分析绘图工具，用于分析不同参数对算法性能的影响：

1. **`device_analysis_plotter.py`** - 无人机数量分析
2. **`workload_analysis_plotter.py`** - 终端数量分析
3. **`parameter_analysis_plotter.py`** - 统一参数分析工具 ⭐ 推荐

## 参数说明

- **M**: 无人机数量 (UAV数量)
- **N**: 终端数量 (UE数量)
- **D_total**: 任务计算量 (每个周期内的总数据量) - 将来扩展
- **v**: Lyapunov权重参数

## 功能特点

- **X轴参数化**：无人机数量(M) 或 终端数量(N)
- **Y轴指标**：时延、能耗、奖励、电池电量、队列长度
- **多条件比较**：支持不同算法、不同v值等条件的组合
- **自动数据聚合**：使用最后10个episode的平均值作为最终性能指标

## 统一工具使用方法

### 无人机数量分析 (M分析)

分析不同无人机数量对性能的影响，固定终端数量和其他参数。

```bash
# 基本用法：比较A2C在不同v值下，固定N=4时，无人机数量对延迟的影响
python parameter_analysis_plotter.py --analysis device \
  --conditions a2c:1.0:4:"A2C v=1.0" a2c:0.0:4:"A2C v=0.0" \
  --metric episode_delays

# 多算法比较：比较不同算法在相同条件下的无人机扩展性
python parameter_analysis_plotter.py --analysis device \
  --conditions a2c:1.0:8:"A2C" ppo:1.0:8:"PPO" ddpg:1.0:8:"DDPG" td3:1.0:8:"TD3" \
  --metric episode_delays

# 能耗分析
python parameter_analysis_plotter.py --analysis device \
  --conditions a2c:1.0:4:"A2C v=1.0" a2c:0.0:4:"A2C v=0.0" \
  --metric episode_energies
```

### 终端数量分析 (N分析)

分析不同终端数量对性能的影响，固定无人机数量和其他参数。

```bash
# 基本用法：比较A2C在不同v值下，固定M=1时，终端数量对延迟的影响
python parameter_analysis_plotter.py --analysis workload \
  --conditions a2c:1.0:1:"A2C v=1.0" a2c:0.0:1:"A2C v=0.0" \
  --metric episode_delays

# 多无人机环境下的终端扩展性
python parameter_analysis_plotter.py --analysis workload \
  --conditions a2c:1.0:2:"A2C M=2" ppo:1.0:2:"PPO M=2" \
  --metric episode_delays

# 能耗随终端数量变化
python parameter_analysis_plotter.py --analysis workload \
  --conditions a2c:1.0:1:"A2C" a2c:0.0:1:"A2C Conservative" \
  --metric episode_energies
```

## 参数格式说明

### 设备分析条件格式
`algorithm:v:N:label`

- `algorithm`: 算法名称 (a2c, ppo, ddpg, td3)
- `v`: v参数值 (0.0, 1.0) 或 'X' (忽略)
- `N`: 固定的任务计算量 (2, 4, 8, 16, 32) 或 'X' (忽略)
- `label`: 可选的自定义标签

### 工作负载分析条件格式
`algorithm:v:M:label`

- `algorithm`: 算法名称
- `v`: v参数值 或 'X'
- `M`: 固定的设备数量 (1, 2, 4, 8, 16) 或 'X'
- `label`: 可选的自定义标签

## 实际使用示例

### 示例1: 设备扩展性分析
```bash
# 分析在固定任务量N=8下，不同算法的设备扩展性
python parameter_analysis_plotter.py --analysis device \
  --conditions \
    a2c:1.0:8:"A2C Aggressive" \
    a2c:0.0:8:"A2C Conservative" \
    ppo:1.0:8:"PPO" \
    ddpg:1.0:8:"DDPG" \
  --metric episode_delays
```

### 示例2: 任务负载分析
```bash
# 分析在单设备环境M=1下，不同算法处理任务负载的能力
python parameter_analysis_plotter.py --analysis workload \
  --conditions \
    a2c:1.0:1:"A2C v=1.0" \
    a2c:0.0:1:"A2C v=0.0" \
    ppo:1.0:1:"PPO" \
  --metric episode_delays
```

### 示例3: 能耗效率分析
```bash
# 设备数量对能耗的影响
python parameter_analysis_plotter.py --analysis device \
  --conditions a2c:1.0:4 a2c:0.0:4 \
  --metric episode_energies

# 任务量对能耗的影响
python parameter_analysis_plotter.py --analysis workload \
  --conditions a2c:1.0:2 a2c:0.0:2 \
  --metric episode_energies
```

### 示例4: 多指标综合分析
```bash
# 延迟分析
python parameter_analysis_plotter.py --analysis device \
  --conditions a2c:1.0:8 ppo:1.0:8 \
  --metric episode_delays

# 能耗分析
python parameter_analysis_plotter.py --analysis device \
  --conditions a2c:1.0:8 ppo:1.0:8 \
  --metric episode_energies

# 电池电量分析
python parameter_analysis_plotter.py --analysis device \
  --conditions a2c:1.0:8 ppo:1.0:8 \
  --metric episode_battery_level
```

## 可用指标

- `episode_delays` - 延迟 (默认)
- `episode_energies` - 能耗
- `episode_rewards` - 奖励
- `episode_battery_level` - 电池电量
- `episode_queue_length` - 队列长度

## 输出文件

生成的图片文件自动命名：
- 设备分析: `device_analysis_{metric}_{algorithms}.png`
- 工作负载分析: `workload_analysis_{metric}_{algorithms}.png`

## 独立工具

如果你更喜欢使用独立的工具：

### 设备分析工具
```bash
python device_analysis_plotter.py \
  --conditions a2c:1.0:4:"A2C v=1.0" a2c:0.0:4:"A2C v=0.0" \
  --metric episode_delays
```

### 工作负载分析工具
```bash
python workload_analysis_plotter.py \
  --conditions a2c:1.0:1:"A2C v=1.0" a2c:0.0:1:"A2C v=0.0" \
  --metric episode_delays
```

## 数据要求

确保存在对应的数据文件：
- `logs/{algorithm}/{config}/evaluations/{metric}.npy`

工具会自动扫描所有匹配条件的配置，并按参数值排序绘制折线图。

## 分析建议

1. **设备扩展性**: 使用设备分析查看算法在不同设备数量下的性能变化
2. **负载处理能力**: 使用工作负载分析查看算法处理不同任务量的能力
3. **参数敏感性**: 比较不同v值对性能的影响
4. **算法对比**: 在相同条件下比较不同算法的表现
