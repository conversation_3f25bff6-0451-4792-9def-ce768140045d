#!/usr/bin/env python3
"""
Parameter Analysis Plotter - 参数分析绘图工具

统一的参数分析工具，支持：
1. 设备数量分析 (x轴为设备数量M)
2. 任务计算量分析 (x轴为任务计算量N)

支持多个算法和不同v等条件的比较。
"""

import argparse
import os
import re

import matplotlib.pyplot as plt
import numpy as np


def parse_config_parameters(config):
    """从配置字符串中解析参数"""
    params = {}

    # 解析v参数
    v_match = re.search(r"v=([0-9.]+)", config)
    if v_match:
        params["v"] = float(v_match.group(1))

    # 解析M参数（设备数量）
    m_match = re.search(r"M=([0-9]+)", config)
    if m_match:
        params["M"] = int(m_match.group(1))

    # 解析N参数（任务计算量）
    n_match = re.search(r"N=([0-9]+)", config)
    if n_match:
        params["N"] = int(n_match.group(1))

    return params


def find_configs_for_analysis(algorithm, analysis_type, fixed_params):
    """
    根据分析类型查找配置

    Args:
        algorithm: 算法名称
        analysis_type: 'device' 或 'workload'
        fixed_params: 固定参数字典，如 {'v': 1.0, 'N': 4}

    Returns:
        list: 匹配的配置列表
    """
    algorithm_dir = f"logs/{algorithm}"
    if not os.path.exists(algorithm_dir):
        return []

    configs = []
    for item in os.listdir(algorithm_dir):
        config_path = os.path.join(algorithm_dir, item)
        if os.path.isdir(config_path):
            # 检查是否有评估数据
            eval_dir = os.path.join(config_path, "evaluations")
            if not os.path.exists(eval_dir):
                continue

            # 解析配置参数
            params = parse_config_parameters(item)

            # 检查固定参数是否匹配
            match = True
            for key, value in fixed_params.items():
                if value is not None and params.get(key) != value:
                    match = False
                    break

            if not match:
                continue

            # 检查是否有变化参数
            if analysis_type == "device" and "M" not in params:
                continue
            elif analysis_type == "workload" and "N" not in params:
                continue

            configs.append(
                {
                    "config": item,
                    "params": params,
                    "x_value": params["M"]
                    if analysis_type == "device"
                    else params["N"],
                }
            )

    # 按x值排序
    configs.sort(key=lambda x: x["x_value"])
    return configs


def plot_parameter_analysis(
    algorithm_conditions,
    analysis_type="device",
    metric="episode_delays",
    show_stats=True,
):
    """
    绘制参数分析图

    Args:
        algorithm_conditions: 算法条件列表
        analysis_type: 'device' 或 'workload'
        metric: 要分析的指标
        show_stats: 是否显示统计信息
    """

    plt.figure(figsize=(12, 8))
    colors = [
        "blue",
        "red",
        "green",
        "orange",
        "purple",
        "brown",
        "pink",
        "gray",
        "cyan",
        "magenta",
    ]
    markers = ["o", "s", "^", "D", "v", "<", ">", "p", "*", "h"]

    # 获取指标和轴标签信息
    if analysis_type == "device":
        x_label = "Number of Devices (M)"
        title_prefix = "vs Device Count"
    else:  # workload
        x_label = "Task Workload (N)"
        title_prefix = "vs Task Workload"

    metric_info = {
        "episode_delays": {"ylabel": "Delay", "title": f"Delay {title_prefix}"},
        "episode_energies": {"ylabel": "Energy", "title": f"Energy {title_prefix}"},
        "episode_rewards": {"ylabel": "Reward", "title": f"Reward {title_prefix}"},
        "episode_battery_level": {
            "ylabel": "Battery Level",
            "title": f"Battery Level {title_prefix}",
        },
        "episode_queue_length": {
            "ylabel": "Queue Length",
            "title": f"Queue Length {title_prefix}",
        },
    }

    info = metric_info.get(
        metric, {"ylabel": metric, "title": f"{metric} {title_prefix}"}
    )

    data_loaded = False

    for i, condition in enumerate(algorithm_conditions):
        algorithm = condition["algorithm"]
        fixed_params = {
            k: v for k, v in condition.items() if k not in ["algorithm", "label"]
        }
        label = condition.get("label", f"{algorithm.upper()}")

        # 查找匹配的配置
        configs = find_configs_for_analysis(algorithm, analysis_type, fixed_params)

        if not configs:
            print(f"No configs found for {label}")
            continue

        x_values = []
        metric_values = []

        for config_info in configs:
            config = config_info["config"]
            x_value = config_info["x_value"]

            # 加载数据
            data_path = f"logs/{algorithm}/{config}/evaluations/{metric}.npy"

            if os.path.exists(data_path):
                try:
                    data = np.load(data_path)

                    # 计算最终性能（最后10个episode的平均值）
                    if len(data) >= 10:
                        final_value = np.mean(data[-10:])
                    else:
                        final_value = np.mean(data)

                    x_values.append(x_value)
                    metric_values.append(final_value)

                except Exception as e:
                    print(f"Error loading {data_path}: {e}")

        if x_values:
            # 按x值排序
            sorted_data = sorted(zip(x_values, metric_values))
            x_values, metric_values = zip(*sorted_data)

            plt.plot(
                x_values,
                metric_values,
                color=colors[i % len(colors)],
                marker=markers[i % len(markers)],
                label=label,
                linewidth=2,
                markersize=8,
                alpha=0.8,
            )

            data_loaded = True
            print(f"Loaded data for {label}: {len(x_values)} configurations")

    if not data_loaded:
        print("No data was loaded. Please check the conditions.")
        return

    # 自定义图表
    plt.xlabel(x_label, fontsize=14)
    plt.ylabel(info["ylabel"], fontsize=14)
    plt.title(info["title"], fontsize=16)
    plt.legend(fontsize=11, loc="best")
    plt.grid(True, alpha=0.3)

    # 设置x轴为整数刻度
    all_x_values = []
    for line in plt.gca().get_lines():
        all_x_values.extend(line.get_xdata())
    plt.gca().set_xticks(sorted(set(all_x_values)))

    plt.tight_layout()

    # 保存图片
    condition_summary = "_".join(
        [f"{c['algorithm']}" for c in algorithm_conditions[:3]]
    )
    if len(algorithm_conditions) > 3:
        condition_summary += f"_and_{len(algorithm_conditions) - 3}_more"

    output_path = f"results/{analysis_type}_analysis_{metric.replace('episode_', '')}_{condition_summary}.png"
    plt.savefig(output_path, dpi=300, bbox_inches="tight")
    print(f"Plot saved as: {output_path}")


def main():
    parser = argparse.ArgumentParser(description="Parameter Analysis Plotter")

    parser.add_argument(
        "--analysis",
        choices=["device", "workload"],
        required=True,
        help="Analysis type: device (M analysis) or workload (N analysis)",
    )

    parser.add_argument(
        "--conditions",
        nargs="+",
        required=True,
        help="Algorithm conditions. Format depends on analysis type:\n"
        "For device analysis: 'algorithm:v:N:label' (e.g., a2c:1.0:4:A2C_v1.0)\n"
        "For workload analysis: 'algorithm:v:M:label' (e.g., a2c:1.0:2:A2C_v1.0)\n"
        "Use 'X' for any parameter to ignore it",
    )

    parser.add_argument(
        "--metric",
        default="episode_delays",
        help="Metric to analyze (default: episode_delays)",
    )

    parser.add_argument("--no-stats", action="store_true", help="Don't show statistics")

    args = parser.parse_args()

    # 解析条件
    algorithm_conditions = []
    for condition_str in args.conditions:
        parts = condition_str.split(":")
        if len(parts) >= 3:
            algorithm = parts[0]
            v_value = float(parts[1]) if parts[1] != "X" else None

            if args.analysis == "device":
                # 对于设备分析，第三个参数是N
                n_value = int(parts[2]) if parts[2] != "X" else None
                label = (
                    parts[3]
                    if len(parts) > 3
                    else f"{algorithm.upper()} v={v_value} N={n_value}"
                )
                condition = {
                    "algorithm": algorithm,
                    "v": v_value,
                    "N": n_value,
                    "label": label,
                }
            else:  # workload
                # 对于工作负载分析，第三个参数是M
                m_value = int(parts[2]) if parts[2] != "X" else None
                label = (
                    parts[3]
                    if len(parts) > 3
                    else f"{algorithm.upper()} v={v_value} M={m_value}"
                )
                condition = {
                    "algorithm": algorithm,
                    "v": v_value,
                    "M": m_value,
                    "label": label,
                }

            algorithm_conditions.append(condition)
        else:
            print(f"Warning: Invalid condition format '{condition_str}'")

    if not algorithm_conditions:
        print("No valid conditions provided")
        return

    plot_parameter_analysis(
        algorithm_conditions=algorithm_conditions,
        analysis_type=args.analysis,
        metric=args.metric,
        show_stats=not args.no_stats,
    )


if __name__ == "__main__":
    main()
